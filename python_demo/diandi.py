import requests
from bs4 import BeautifulSoup

headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Pragma": "no-cache",
    "Referer": "https://www.qidian.com/chapter/1115277/********/",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\""
}
cookies = {
    "supportwebp": "true",
    "newstatisticUUID": "**********_753966708",
    "_csrfToken": "kymfIZ8FEo1fYDYHizlNqtVh0AcvMYwcAyJHkpJW",
    "fu": "**********",
    "supportWebp": "true",
    "traffic_search_engine": "",
    "traffic_utm_referer": "",
    "Hm_lvt_f00f67093ce2f38f215010b699629083": "**********,**********",
    "HMACCOUNT": "317680DB6A1F48C3",
    "se_ref": "",
    "_gid": "GA1.2.*********.**********",
    "Hm_lpvt_f00f67093ce2f38f215010b699629083": "**********",
    "_ga": "GA1.2.**********.**********",
    "x-waf-captcha-referer": "https%3A%2F%2Fwww.qidian.com%2Fchapter%2F1115277%2F********%2F",
    "_ga_PFYW0QLV3P": "GS2.1.s**********$o2$g1$t1753249998$j60$l0$h0",
    "_ga_FZMMH98S83": "GS2.1.s**********$o2$g1$t1753249998$j60$l0$h0",
    "w_tsfp": "ltv2UU8E3ewC6mwF46vukUisEjEufDElkgFsXqNmeJ94Q7ErU5mA0oR+t8L+OXTY68xnt9jMsoszd3qAUdMmfxUdTMWYco4YkB/Gy99yicxUQ0k5VYnWS18dcLtz7TIXf29dJUHh2G4rIIAQnOBoj14Kt3VymPtxXvFqL5kXjB0ZufzCkpxuDW3HlFWQRzaZciVfKr/c9OtwraxQ9z/c5Vv7LFt0A6hewgfHg31dWzox6wPjMK0ddgmuUtutLvgy23S0hSe2M8T1iEg9sg9qpRxLUY2lmXGfbWNGVgpqZF2whbkxedipNOYgsTkcVblNP0sJu0pb7PAz6xwLSWj+dXb+EPgMoAJ2AbUe4Yz1LXKUiIS/f1VM6qt9zQp3/8dH+joEZmj2KNxdT3TJE3ENeYgIY5myIShEQ19BDm8ZtU5VeSVKAfkkbtacvhG+L0tb0Oc0NbHuK7Zeay7MBPC2UeQxCjK8+4Ar"
}
url = "https://www.qidian.com/chapter/1115277/********/"
response = requests.get(url, headers=headers, cookies=cookies)

# 解析HTML
soup = BeautifulSoup(response.text, 'html.parser')
print(soup.prettify())

# 定位main标签（通过id和class双重筛选，确保准确性）
main_tag = soup.find('main', id='c-********', class_='content')

# 存储提取的正文内容
content_texts = []
# print(main_tag.text)
if main_tag:
    # 遍历main下的所有p标签
    for p_tag in main_tag.find_all('p'):
        print(p_tag,p_tag.get_text())
        content_texts.append(p_tag.get_text())
        # content_texts.append(p_tag.text)
        # 查找每个p标签中class为content-text的span标签
        content_span = p_tag.find('span', class_='content-text')
        # print(content_span)

        # if content_span:
        #     # 提取文本并去除首尾空白（包括开头的全角空格）
        #     text = content_span.get_text(strip=True)
        #     if text:  # 排除空文本
        #         content_texts.append(text)




#
# # 输出结果
# print("提取的正文内容：")
# for i, text in enumerate(content_texts, 1):
#     print(f"第{i}段：{text}")


print('*********************')

# 解析HTML
soup = BeautifulSoup(response.text, 'html.parser')
# 查找main标签
main_tag = soup.find('main',id='c-********', class_='content')

# 初始化一个列表存储结果
span_texts = []

if main_tag:
    # 查找main下所有的p标签
    p_tags = main_tag.find_all('p')
    for p in p_tags:
        # 查找每个p标签下的span标签
        print(p,'========')

        span_tags = p.find('span', class_='content-text')
        print(span_tags)
        if span_tags:
            span_texts.append(span.get_text(strip=True))
        # print(span_tags)
        # for span in span_tags:
        #     # 获取span标签的文本内容并添加到列表
        #     span_texts.append(span.get_text(strip=True))


# 输出结果
# print("提取到的span文字内容：")
for text in span_texts:
    print(text)


# print(response.text)
# print('--------------')
# print(response)