from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time
import json

class IPScraper:
    def __init__(self, headless=True):
        self.setup_driver(headless)
    
    def setup_driver(self, headless):
        """设置Chrome浏览器"""
        chrome_options = Options()
        
        if headless:
            chrome_options.add_argument('--headless')  # 无头模式
        
        # 反检测设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置User-Agent
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("Chrome浏览器启动成功")
        except Exception as e:
            print(f"浏览器启动失败: {e}")
            print("请确保已安装ChromeDriver")
            raise
    
    def get_ips(self, url='https://www.kuaidaili.com/free/fps/', max_pages=3):
        """获取IP列表"""
        all_ips = []
        
        try:
            for page in range(1, max_pages + 1):
                page_url = f"{url}{page}/" if page > 1 else url
                print(f"\n正在爬取第 {page} 页: {page_url}")
                
                # 访问页面
                self.driver.get(page_url)
                
                # 等待页面加载
                print("等待页面加载...")
                time.sleep(3)
                
                # 等待表格加载
                try:
                    wait = WebDriverWait(self.driver, 10)
                    table = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "table")))
                    print("表格加载完成")
                except Exception as e:
                    print(f"表格加载超时: {e}")
                    continue
                
                # 提取IP数据
                page_ips = self.extract_ip_data()
                
                if page_ips:
                    all_ips.extend(page_ips)
                    print(f"第 {page} 页获取到 {len(page_ips)} 个IP")
                else:
                    print(f"第 {page} 页未获取到IP数据")
                
                # 页面间延时
                if page < max_pages:
                    time.sleep(2)
        
        except Exception as e:
            print(f"爬取过程出错: {e}")
        
        return all_ips
    
    def extract_ip_data(self):
        """提取IP数据"""
        ips = []
        
        try:
            # 多种选择器尝试
            selectors = [
                "table tbody tr",
                ".table tbody tr", 
                "[class*='table'] tbody tr",
                "tbody tr"
            ]
            
            rows = None
            for selector in selectors:
                try:
                    rows = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if rows:
                        print(f"使用选择器 '{selector}' 找到 {len(rows)} 行数据")
                        break
                except:
                    continue
            
            if not rows:
                print("未找到表格行数据")
                return ips
            
            for row in rows:
                try:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) >= 2:
                        ip = cells[0].text.strip()
                        port = cells[1].text.strip()
                        
                        if ip and port and self.is_valid_ip(ip):
                            # 获取更多信息
                            location = cells[2].text.strip() if len(cells) > 2 else ""
                            anonymity = cells[3].text.strip() if len(cells) > 3 else ""
                            protocol = cells[4].text.strip() if len(cells) > 4 else ""
                            
                            ip_data = {
                                'ip': ip,
                                'port': port,
                                'location': location,
                                'anonymity': anonymity,
                                'protocol': protocol,
                                'proxy': f"{ip}:{port}"
                            }
                            ips.append(ip_data)
                            
                except Exception as e:
                    continue
        
        except Exception as e:
            print(f"数据提取失败: {e}")
        
        return ips
    
    def is_valid_ip(self, ip):
        """验证IP格式"""
        try:
            parts = ip.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False
    
    def save_ips(self, ips, filename='proxy_ips.json'):
        """保存IP数据"""
        if ips:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(ips, f, ensure_ascii=False, indent=2)
                print(f"\n数据已保存到 {filename}")
                
                # 同时保存为简单格式
                simple_filename = filename.replace('.json', '_simple.txt')
                with open(simple_filename, 'w', encoding='utf-8') as f:
                    for ip_data in ips:
                        f.write(f"{ip_data['proxy']}\n")
                print(f"简单格式已保存到 {simple_filename}")
                
                return True
            except Exception as e:
                print(f"保存失败: {e}")
                return False
        return False
    
    def test_proxy(self, proxy, timeout=5):
        """测试代理可用性"""
        import requests
        
        try:
            proxies = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            
            response = requests.get('http://httpbin.org/ip', 
                                  proxies=proxies, 
                                  timeout=timeout)
            
            if response.status_code == 200:
                return True, response.json()
            else:
                return False, None
                
        except Exception as e:
            return False, str(e)
    
    def close(self):
        """关闭浏览器"""
        if hasattr(self, 'driver'):
            self.driver.quit()
            print("浏览器已关闭")

def main():
    scraper = None
    
    try:
        print("=" * 60)
        print("开始爬取快代理IP")
        print("=" * 60)
        
        # 创建爬虫实例
        scraper = IPScraper(headless=False)  # 设置为False可以看到浏览器操作
        
        # 爬取IP
        ips = scraper.get_ips(max_pages=2)
        
        if ips:
            print(f"\n总共获取到 {len(ips)} 个代理IP:")
            print("-" * 50)
            
            # 显示前10个
            for i, ip_data in enumerate(ips[:10], 1):
                print(f"{i:2d}. {ip_data['proxy']:20} | {ip_data['location']:15} | {ip_data['anonymity']}")
            
            if len(ips) > 10:
                print(f"... 还有 {len(ips) - 10} 个IP")
            
            # 保存数据
            scraper.save_ips(ips)
            
            # 测试前3个代理
            print(f"\n测试前3个代理的可用性:")
            print("-" * 40)
            for i, ip_data in enumerate(ips[:3], 1):
                proxy = ip_data['proxy']
                print(f"测试 {proxy}...", end=' ')
                is_valid, result = scraper.test_proxy(proxy)
                if is_valid:
                    print("✅ 可用")
                else:
                    print("❌ 不可用")
        
        else:
            print("未获取到任何IP数据")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
    
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    main()