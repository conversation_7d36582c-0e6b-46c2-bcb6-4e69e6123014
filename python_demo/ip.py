import requests
import json
from lxml import etree
import numpy as np
# num = np.arange(2,10,2)
# print(num)
#
# for i,k in enumerate(num):
#     print(i,k)

url = 'https://www.kuaidaili.com/free/fps/'
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3",
    "Referer": "https://www.kuaidaili.com/free/",  # Referer 模拟来源页
}
#
response = requests.get(url,headers=HEADERS, timeout=10)
response.raise_for_status()
response.encoding = 'utf-8'
print(response.text)
e = etree.HTML(response.text)
#
#
ips = e.xpath('//div[@class="kdl-table-wrapper bordered "]/table/tbody/tr/td[1]/text()')
ports = e.xpath('//div[@class="kdl-table-wrapper bordered "]/table/tbody/tr/td[2]/text()')
for ip,port in zip(ips,ports):
    print(ip,port)



