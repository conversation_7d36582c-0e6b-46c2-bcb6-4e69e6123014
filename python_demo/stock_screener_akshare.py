#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用akshare库的股票筛选器
筛选条件：
1. 热股榜前100
2. 昨天最高价为最近200个交易日最高价
3. 股价小于200元

安装依赖：
pip install akshare pandas requests
"""

import akshare as ak
import pandas as pd
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class StockScreenerAK:
    def __init__(self):
        print("初始化股票筛选器...")
        
    def get_hot_stocks(self, limit=100):
        """获取热门股票列表"""
        print(f"正在获取热门股票前{limit}只...")
        
        try:
            # 获取沪深A股实时行情
            stock_zh_a_spot_em_df = ak.stock_zh_a_spot_em()
            
            # 按成交量排序，取前100只作为热门股票
            hot_stocks = stock_zh_a_spot_em_df.nlargest(limit, '成交量')
            
            result = []
            for _, row in hot_stocks.iterrows():
                result.append({
                    'code': row['代码'],
                    'name': row['名称'],
                    'price': float(row['最新价']),
                    'volume': row['成交量'],
                    'change_pct': row['涨跌幅']
                })
            
            print(f"成功获取{len(result)}只热门股票")
            return result
            
        except Exception as e:
            print(f"获取热门股票失败: {e}")
            return []

    def get_stock_history(self, stock_code, days=200):
        """获取股票历史数据"""
        try:
            # 计算开始日期
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days*2)).strftime('%Y%m%d')  # 多取一些确保有足够交易日
            
            # 获取历史数据
            stock_zh_a_hist_df = ak.stock_zh_a_hist(
                symbol=stock_code, 
                period="daily", 
                start_date=start_date, 
                end_date=end_date, 
                adjust=""
            )
            
            if stock_zh_a_hist_df is not None and len(stock_zh_a_hist_df) > 0:
                # 只取最近200个交易日
                stock_zh_a_hist_df = stock_zh_a_hist_df.tail(days).reset_index(drop=True)
                return stock_zh_a_hist_df
            else:
                return None
                
        except Exception as e:
            print(f"获取股票 {stock_code} 历史数据失败: {e}")
            return None

    def check_conditions(self, stock_info, history_df):
        """检查股票是否符合筛选条件"""
        if history_df is None or len(history_df) < 2:
            return False, "历史数据不足"
        
        stock_code = stock_info['code']
        current_price = stock_info['price']
        
        # 条件1: 股价小于200元
        if current_price >= 200:
            return False, f"股价{current_price:.2f}元 >= 200元"
        
        # 获取昨天的最高价（最后一个交易日）
        yesterday_high = float(history_df.iloc[-1]['最高'])
        
        # 获取最近200个交易日的最高价
        max_high_200days = float(history_df['最高'].max())
        
        # 条件2: 昨天最高价为最近200个交易日最高价
        if abs(yesterday_high - max_high_200days) < 0.01:  # 考虑浮点数精度
            return True, f"昨日最高价{yesterday_high:.2f}元为200日最高价，当前价{current_price:.2f}元"
        else:
            return False, f"昨日最高价{yesterday_high:.2f}元，200日最高价{max_high_200days:.2f}元"

    def screen_stocks(self, limit=100):
        """主要筛选函数"""
        print("=" * 80)
        print("开始筛选股票...")
        print("筛选条件：")
        print("1. 热门股票前100（按成交量排序）")
        print("2. 昨天最高价为最近200个交易日最高价")
        print("3. 股价小于200元")
        print("=" * 80)
        
        # 获取热门股票
        hot_stocks = self.get_hot_stocks(limit)
        if not hot_stocks:
            print("未能获取热门股票数据")
            return []
        
        qualified_stocks = []
        
        for i, stock in enumerate(hot_stocks, 1):
            print(f"\n[{i:3d}/{len(hot_stocks)}] 检查股票: {stock['code']} {stock['name']}")
            print(f"    当前价: {stock['price']:.2f}元, 涨跌幅: {stock['change_pct']:.2f}%")
            
            # 获取历史数据
            history_df = self.get_stock_history(stock['code'])
            
            # 检查条件
            is_qualified, reason = self.check_conditions(stock, history_df)
            
            if is_qualified:
                qualified_stocks.append({
                    'code': stock['code'],
                    'name': stock['name'],
                    'price': stock['price'],
                    'change_pct': stock['change_pct'],
                    'volume': stock['volume'],
                    'reason': reason
                })
                print(f"    ✅ {reason}")
            else:
                print(f"    ❌ {reason}")
            
            # 避免请求过于频繁
            time.sleep(0.2)
        
        return qualified_stocks

    def save_results(self, qualified_stocks):
        """保存筛选结果"""
        print("\n" + "=" * 80)
        
        if not qualified_stocks:
            print("没有找到符合条件的股票")
            return
        
        print(f"找到 {len(qualified_stocks)} 只符合条件的股票:")
        print("-" * 80)
        
        # 打印结果表格
        print(f"{'序号':>4} {'代码':>8} {'名称':>10} {'当前价':>8} {'涨跌幅':>8} {'成交量':>12} {'说明'}")
        print("-" * 80)
        
        for i, stock in enumerate(qualified_stocks, 1):
            volume_str = f"{stock['volume']/10000:.0f}万" if stock['volume'] < 100000000 else f"{stock['volume']/100000000:.1f}亿"
            print(f"{i:4d} {stock['code']:>8} {stock['name']:>10} "
                  f"{stock['price']:>8.2f} {stock['change_pct']:>7.2f}% "
                  f"{volume_str:>12} 创200日新高")
        
        # 保存到CSV文件
        if qualified_stocks:
            df = pd.DataFrame(qualified_stocks)
            filename = f"qualified_stocks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n结果已保存到: {filename}")
            
        print("=" * 80)

def main():
    """主函数"""
    print("股票筛选器 - 寻找创新高的优质股票")
    print("数据来源: akshare")
    print()
    
    screener = StockScreenerAK()
    
    try:
        # 开始筛选
        qualified_stocks = screener.screen_stocks(limit=100)
        
        # 保存结果
        screener.save_results(qualified_stocks)
        
        print("\n筛选完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
