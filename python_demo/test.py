import numpy as np
import matplotlib.pyplot as mpl
from mpl_toolkits.mplot3d import axes3d

pred_prices = np.zeros(30-6)
for i in range(pred_prices.size):
    A =np.zeros((3,3))
    for j in range(3):
        print(i +j,i+j+3,j)
        # A[j,]=closing_prices[i +j:i+j+3]
    # B= closing_prices[i+3:i+6]
    # print()
    # x=np.linalg.lstsq(A,B)[0]
    # pred=B.dot(x) 
#点乘 两数组对应位置相乘再相加pred prices[i]= predmp.plot(dates[6:], pred prices,'o-color='orangered', label='Prediction')
pass                                                                     





