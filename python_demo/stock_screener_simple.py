#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版股票筛选器 - 不依赖外部库
使用网络请求获取股票数据
"""

import requests
import json
import time
from datetime import datetime, timedelta
import csv

class SimpleStockScreener:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://finance.sina.com.cn/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def get_hot_stocks_sina(self, limit=100):
        """从新浪财经获取热门股票"""
        print(f"正在获取热门股票前{limit}只...")
        
        try:
            # 新浪财经沪深A股数据
            url = "http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData"
            params = {
                'page': 1,
                'num': limit,
                'sort': 'volume',  # 按成交量排序
                'asc': 0,  # 降序
                'node': 'hs_a',  # 沪深A股
                'symbol': '',
                '_s_r_a': 'page'
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.text
                if data and data != 'null':
                    stocks_data = json.loads(data)
                    
                    result = []
                    for stock in stocks_data:
                        if len(result) >= limit:
                            break
                        
                        try:
                            price = float(stock['trade'])
                            if price > 0:  # 过滤停牌股票
                                result.append({
                                    'code': stock['symbol'],
                                    'name': stock['name'],
                                    'price': price,
                                    'volume': int(stock['volume']),
                                    'change_pct': float(stock['changepercent'])
                                })
                        except (ValueError, KeyError):
                            continue
                    
                    print(f"成功获取{len(result)}只热门股票")
                    return result
            
            print("获取热门股票失败，使用测试数据")
            return self.get_test_stocks(limit)
            
        except Exception as e:
            print(f"获取热门股票出错: {e}")
            return self.get_test_stocks(limit)

    def get_test_stocks(self, limit=20):
        """测试用股票数据"""
        test_stocks = [
            {'code': 'sh600036', 'name': '招商银行', 'price': 45.60, 'volume': 50000000, 'change_pct': 2.1},
            {'code': 'sz000001', 'name': '平安银行', 'price': 12.50, 'volume': 80000000, 'change_pct': 1.8},
            {'code': 'sz000002', 'name': '万科A', 'price': 18.20, 'volume': 60000000, 'change_pct': -0.5},
            {'code': 'sz002415', 'name': '海康威视', 'price': 35.20, 'volume': 40000000, 'change_pct': 3.2},
            {'code': 'sz300059', 'name': '东方财富', 'price': 25.80, 'volume': 90000000, 'change_pct': 4.5},
            {'code': 'sh600519', 'name': '贵州茅台', 'price': 1800.00, 'volume': 5000000, 'change_pct': 1.2},
            {'code': 'sz000858', 'name': '五粮液', 'price': 180.50, 'volume': 15000000, 'change_pct': 2.8},
            {'code': 'sh600887', 'name': '伊利股份', 'price': 32.40, 'volume': 25000000, 'change_pct': 1.5},
        ]
        return test_stocks[:limit]

    def get_stock_history_sina(self, stock_code, days=200):
        """从新浪财经获取股票历史数据"""
        try:
            # 新浪财经历史数据API
            url = "http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData"
            params = {
                'symbol': stock_code,
                'scale': '240',  # 日线
                'ma': 'no',
                'datalen': days + 50  # 多获取一些确保有足够交易日
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.text
                if data and data != 'null':
                    history_data = json.loads(data)
                    
                    if history_data and len(history_data) >= days:
                        # 只取最近200个交易日
                        recent_data = history_data[-days:]
                        return recent_data
            
            return None
            
        except Exception as e:
            print(f"获取 {stock_code} 历史数据失败: {e}")
            return None

    def check_conditions(self, stock_info, history_data):
        """检查股票是否符合筛选条件"""
        if not history_data or len(history_data) < 2:
            return False, "历史数据不足"
        
        current_price = stock_info['price']
        
        # 条件1: 股价小于200元
        if current_price >= 200:
            return False, f"股价{current_price:.2f}元 >= 200元"
        
        try:
            # 获取昨天的最高价（最后一个交易日）
            yesterday_high = float(history_data[-1]['high'])
            
            # 获取最近200个交易日的最高价
            max_high_200days = max(float(day['high']) for day in history_data)
            
            # 条件2: 昨天最高价为最近200个交易日最高价
            if abs(yesterday_high - max_high_200days) < 0.01:
                return True, f"昨日最高价{yesterday_high:.2f}元为200日最高价"
            else:
                return False, f"昨日最高价{yesterday_high:.2f}元，200日最高价{max_high_200days:.2f}元"
                
        except (ValueError, KeyError) as e:
            return False, f"数据解析错误: {e}"

    def screen_stocks(self, limit=50):
        """主要筛选函数"""
        print("=" * 60)
        print("简化版股票筛选器")
        print("筛选条件：")
        print("1. 热门股票（按成交量排序）")
        print("2. 昨天最高价为最近200个交易日最高价")
        print("3. 股价小于200元")
        print("=" * 60)
        
        # 获取热门股票
        hot_stocks = self.get_hot_stocks_sina(limit)
        if not hot_stocks:
            print("未能获取热门股票数据")
            return []
        
        qualified_stocks = []
        
        for i, stock in enumerate(hot_stocks, 1):
            print(f"\n[{i:2d}/{len(hot_stocks)}] {stock['code']} {stock['name']} - {stock['price']:.2f}元")
            
            # 获取历史数据
            history_data = self.get_stock_history_sina(stock['code'])
            
            # 检查条件
            is_qualified, reason = self.check_conditions(stock, history_data)
            
            if is_qualified:
                qualified_stocks.append(stock)
                print(f"    ✅ {reason}")
            else:
                print(f"    ❌ {reason}")
            
            # 避免请求过于频繁
            time.sleep(0.3)
        
        return qualified_stocks

    def save_results(self, qualified_stocks):
        """保存筛选结果"""
        print("\n" + "=" * 60)
        
        if not qualified_stocks:
            print("没有找到符合条件的股票")
            return
        
        print(f"找到 {len(qualified_stocks)} 只符合条件的股票:")
        print("-" * 60)
        
        for i, stock in enumerate(qualified_stocks, 1):
            volume_str = f"{stock['volume']/10000:.0f}万" if stock['volume'] < 100000000 else f"{stock['volume']/100000000:.1f}亿"
            print(f"{i:2d}. {stock['code']} {stock['name']:8s} "
                  f"价格:{stock['price']:6.2f}元 成交量:{volume_str}")
        
        # 保存到CSV文件
        filename = f"qualified_stocks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['code', 'name', 'price', 'volume', 'change_pct']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for stock in qualified_stocks:
                writer.writerow(stock)
        
        print(f"\n结果已保存到: {filename}")

def main():
    """主函数"""
    print("简化版股票筛选器")
    print("无需安装额外依赖，使用Python标准库")
    print()
    
    screener = SimpleStockScreener()
    
    try:
        qualified_stocks = screener.screen_stocks(limit=30)
        screener.save_results(qualified_stocks)
        print("\n筛选完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序执行出错: {e}")

if __name__ == "__main__":
    main()
