import requests
from holoviews.examples.gallery.apps.bokeh.game_of_life import title
from imageio.v3 import improps
from lxml import etree
import json
import pandas as pd
from bs4 import BeautifulSoup
# url = 'https://www.qidian.com/chapter/1115277/22087045/'
url = 'https://dl.131437.xyz/book/jiuzhouhuzhufuren/26947.html'
num = 1
while True:
    num += 1
    headers = {
        'User-Agent':'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
    }
    result = requests.get(url,headers=headers)
    result.encoding = 'utf-8'
    # print(result.text)

    e = etree.HTML(result.text)
    # print(e)
    info = '\n'.join(e.xpath('//div[@class="m-post"]/p/text()'))
    arcicle = e.xpath('//h1/text()')[0]
    url = f'https://dl.131437.xyz{e.xpath("//table/tbody/tr/td/a/@href")[1]}'
    # print(url)
    # print(info,titletext)

    with open('./xiaoshuo'+arcicle+'.txt','w',encoding='utf-8') as f:
        f.write(arcicle+'\n\n'+info+'\n\n')
        f.close()
    if(num==10):
        break

    # # title = e.xpath('//h1')
    # print(info)


# 解析HTML
soup = BeautifulSoup(response.text, 'html.parser')
# 查找main标签
main_tag = soup.find('main',id='c-22058859')

# 初始化一个列表存储结果
span_texts = []

if main_tag:
    # 查找main下所有的p标签
    p_tags = main_tag.find_all('p')
    print(p_tags)

    for p in p_tags:
        # 查找每个p标签下的span标签
        span_tags = p.find_all('span')
        print(span_tags)
        for span in span_tags:
            # 获取span标签的文本内容并添加到列表
            span_texts.append(span.get_text(strip=True))

