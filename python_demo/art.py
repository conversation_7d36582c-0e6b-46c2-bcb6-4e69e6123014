from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time

# 配置 Chrome 浏览器
options = webdriver.ChromeOptions()
# 添加请求头（模拟真实浏览器）
options.add_argument(
    "user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
# 可选：无头模式（不显示浏览器窗口，加快运行速度）
# options.add_argument("--headless=new")

# 初始化浏览器驱动
driver = webdriver.Chrome(
    service=Service(ChromeDriverManager().install()),
    options=options
)

# 目标 URL
url = "https://www.qidian.com/chapter/1115277/22058859/"

try:
    # 加载页面
    driver.get(url)

    # 等待页面动态内容加载（根据实际情况调整时间，起点中文网加载较快，3秒足够）
    time.sleep(3)

    # 获取包含动态内容的完整页面源码
    page_source = driver.page_source

    # 解析 HTML
    soup = BeautifulSoup(page_source, 'html.parser')

    # 定位 main 标签
    main_tag = soup.find('main', id='c-22058859', class_='content')

    # 存储提取的正文内容
    content_texts = []

    if main_tag:
        # 遍历所有 p 标签，提取 content-text 类 span 的内容
        for p_tag in main_tag.find_all('p'):
            content_span = p_tag.find('span', class_='content-text')
            if content_span:
                text = content_span.get_text(strip=True)
                if text:
                    content_texts.append(text)
                    print(f"提取内容：{text}")

    # 输出最终结果
    print("\n所有提取的正文段落：")
    for i, text in enumerate(content_texts, 1):
        print(f"第{i}段：{text}")

finally:
    # 关闭浏览器
    driver.quit()