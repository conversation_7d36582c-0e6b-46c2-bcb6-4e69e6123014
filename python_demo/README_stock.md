# 股票筛选器

这是一个Python股票筛选工具，用于从热门股票中筛选出符合特定条件的个股。

## 筛选条件

1. **热股榜前100** - 按成交量排序的热门股票
2. **昨天最高价为最近200个交易日最高价** - 创新高的股票
3. **股价小于200元** - 排除高价股

## 文件说明

### 1. stock_screener_akshare.py（推荐）
- 使用akshare库获取真实股票数据
- 数据准确，功能完整
- 需要安装依赖包

### 2. stock_screener_simple.py（备用）
- 使用Python标准库和网络请求
- 无需安装额外依赖
- 适合快速测试

### 3. install_requirements.py
- 自动安装依赖包的脚本

## 安装和使用

### 方法1：使用akshare版本（推荐）

```bash
# 1. 安装依赖
cd python_demo
python install_requirements.py

# 2. 运行筛选器
python stock_screener_akshare.py
```

### 方法2：使用简化版本

```bash
# 直接运行，无需安装依赖
cd python_demo
python stock_screener_simple.py
```

## 输出示例

```
找到 3 只符合条件的股票:
----------------------------------------------------------------
序号     代码       名称     当前价     涨跌幅       成交量 说明
----------------------------------------------------------------
   1   000001       平安银行    12.50     2.50%        5.2亿 创200日新高
   2   002415     海康威视    35.20     1.80%        3.8亿 创200日新高
   3   300059     东方财富    25.80     3.20%        8.9亿 创200日新高

结果已保存到: qualified_stocks_20231201_143022.csv
```

## 自定义参数

可以修改以下参数来调整筛选条件：

```python
# 修改热股榜数量（默认100）
qualified_stocks = screener.screen_stocks(limit=50)

# 修改股价上限（在check_conditions函数中）
if current_price >= 100:  # 改为100元

# 修改历史数据天数（默认200天）
history_df = self.get_stock_history(stock['code'], days=100)
```

## 注意事项

1. **网络连接** - 需要稳定的网络连接获取股票数据
2. **运行时间** - 筛选100只股票大约需要5-10分钟
3. **数据延迟** - 股票数据可能有15-20分钟延迟
4. **请求频率** - 程序已加入延时避免请求过于频繁

## 常见问题

### Q: 提示"获取数据失败"
A: 检查网络连接，或稍后重试

### Q: akshare安装失败
A: 使用简化版本 `stock_screener_simple.py`

### Q: 没有找到符合条件的股票
A: 可以适当放宽筛选条件，比如增加股价上限

### Q: 程序运行很慢
A: 这是正常现象，因为需要获取每只股票的历史数据

## 免责声明

本工具仅用于技术分析和学习目的，不构成投资建议。投资有风险，入市需谨慎。
