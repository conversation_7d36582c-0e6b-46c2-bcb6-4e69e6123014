import requests
from bs4 import BeautifulSoup
import json
import time
import random
import os
from datetime import datetime


class MovieFinder:
    """电影资源信息收集器 - 仅收集合法免费资源"""

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 创建数据目录
        self.data_dir = 'movie_data'
        os.makedirs(self.data_dir, exist_ok=True)

    def search_movie_info(self, title):
        """搜索电影信息 - 使用公开API"""
        print(f"搜索电影: {title}")

        # 使用OMDb API (需要申请免费密钥)
        # 这里使用示例密钥，实际使用请申请: http://www.omdbapi.com/apikey.aspx
        api_key = "sample_key"  # 替换为你的API密钥
        url = f"http://www.omdbapi.com/?t={title}&apikey={api_key}"

        try:
            response = self.session.get(url)
            data = response.json()

            if data.get('Response') == 'True':
                print(f"找到电影: {data.get('Title')} ({data.get('Year')})")
                return data
            else:
                print(f"未找到电影信息: {data.get('Error', '未知错误')}")
                return None

        except Exception as e:
            print(f"搜索出错: {e}")
            return None

    def find_free_resources(self, title):
        """查找免费资源 - 仅搜索合法免费资源"""
        print(f"查找免费资源: {title}")

        # 这里使用Archive.org作为示例 - 它提供合法的公共领域电影
        url = f"https://archive.org/search.php?query={title}&and[]=mediatype%3A%22movies%22"

        try:
            response = self.session.get(url)
            soup = BeautifulSoup(response.text, 'html.parser')

            results = []
            items = soup.select('.item-ia')

            for item in items[:5]:  # 限制结果数量
                title_elem = item.select_one('.ttl')
                link_elem = item.select_one('.item-ttl a')

                if title_elem and link_elem:
                    item_title = title_elem.text.strip()
                    item_link = "https://archive.org" + link_elem['href']

                    results.append({
                        'title': item_title,
                        'url': item_link,
                        'source': 'Archive.org',
                        'is_free': True,
                        'is_legal': True
                    })

            return results

        except Exception as e:
            print(f"查找资源出错: {e}")
            return []

    def get_movie_recommendations(self, genre=None):
        """获取电影推荐 - 基于公开数据"""
        print(f"获取电影推荐 (类型: {genre or '所有'})")

        # 这里使用示例数据，实际应用可以连接到TMDB等公开API
        genres = {
            "action": ["速度与激情", "碟中谍", "复仇者联盟"],
            "comedy": ["疯狂动物城", "飞屋环游记", "功夫熊猫"],
            "drama": ["肖申克的救赎", "阿甘正传", "当幸福来敲门"],
            "sci-fi": ["星际穿越", "头号玩家", "火星救援"]
        }

        if genre and genre.lower() in genres:
            return genres[genre.lower()]
        else:
            # 返回混合推荐
            all_movies = []
            for movies in genres.values():
                all_movies.extend(movies)

            # 随机选择5部
            return random.sample(all_movies, min(5, len(all_movies)))

    def create_watchlist(self, movies, filename=None):
        """创建观影清单"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.data_dir}/watchlist_{timestamp}.json"

        watchlist = {
            "created_at": datetime.now().isoformat(),
            "movies": movies
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(watchlist, f, ensure_ascii=False, indent=2)

        print(f"观影清单已保存到: {filename}")
        return filename


def main():
    finder = MovieFinder()

    print("=" * 60)
    print("电影资源查找器 (仅限合法免费资源)")
    print("=" * 60)

    while True:
        print("\n选择操作:")
        print("1. 搜索电影信息")
        print("2. 查找免费资源")
        print("3. 获取电影推荐")
        print("4. 创建观影清单")
        print("5. 退出")

        choice = input("\n请输入选项 (1-5): ")

        if choice == '1':
            title = input("请输入电影名称: ")
            finder.search_movie_info(title)

        elif choice == '2':
            title = input("请输入电影名称: ")
            resources = finder.find_free_resources(title)

            if resources:
                print(f"\n找到 {len(resources)} 个资源:")
                for i, res in enumerate(resources, 1):
                    print(f"{i}. {res['title']} - {res['source']}")
                    print(f"   链接: {res['url']}")
            else:
                print("未找到相关资源")

        elif choice == '3':
            print("\n电影类型:")
            print("1. 动作片")
            print("2. 喜剧片")
            print("3. 剧情片")
            print("4. 科幻片")
            print("5. 混合推荐")

            genre_choice = input("请选择类型 (1-5): ")
            genre_map = {
                '1': 'action',
                '2': 'comedy',
                '3': 'drama',
                '4': 'sci-fi',
                '5': None
            }

            genre = genre_map.get(genre_choice)
            movies = finder.get_movie_recommendations(genre)

            print("\n推荐电影:")
            for i, movie in enumerate(movies, 1):
                print(f"{i}. {movie}")

        elif choice == '4':
            movies = []
            print("创建观影清单 (输入空行完成)")

            while True:
                movie = input("添加电影 (留空结束): ")
                if not movie:
                    break
                movies.append({"title": movie, "added_at": datetime.now().isoformat()})

            if movies:
                finder.create_watchlist(movies)
            else:
                print("未添加任何电影")

        elif choice == '5':
            print("感谢使用，再见！")
            break

        else:
            print("无效选项，请重新选择")

        time.sleep(1)


if __name__ == "__main__":
    main()