import numpy as np
import matplotlib.pyplot as mp
from mpl_toolkits.mplot3d import axes3d
from matplotlib.ticker import MultipleLocator


import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.animation import FuncAnimation

# 创建图形和3D坐标轴
fig = plt.figure(figsize=(10, 8))
ax = fig.add_subplot(111, projection='3d')

# 设置坐标轴范围
ax.set_xlim(-1, 1)
ax.set_ylim(-1, 1)
ax.set_zlim(-1, 1)

# 生成初始散点数据
num_points = 50
x = np.random.rand(num_points) * 2 - 1
y = np.random.rand(num_points) * 2 - 1
z = np.random.rand(num_points) * 2 - 1

# 创建散点图对象
scatter = ax.scatter(x, y, z, c='b', marker='o', s=30)

# 更新函数，每一帧都会调用此函数
def update(frame):
    # 计算新位置（简单的圆周运动）
    global x, y, z
    t = frame * 0.02  # 时间参数
    x = np.sin(t + np.arange(num_points)) * 0.5
    y = np.cos(t + np.arange(num_points)) * 0.5
    z = np.sin(t * 2 + np.arange(num_points)) * 0.3
    
    # 更新散点位置
    scatter._offsets3d = (x, y, z)
    return scatter,

# 创建动画
ani = FuncAnimation(fig, update, frames=np.arange(0, 100), interval=50, blit=True)

plt.show()


pass
n=1000
x,y = np.meshgrid(np.linspace(-3,3,n),np.linspace(-3,3,n))
z=(1 - x/2 + x**5 + y**3)* np.exp(-x**2 - y**2)

# fig = mp.figure('3d')
# ax3d = mp.gca(projection='3d')
# ax3d.set_xlabel('x')
# ax3d.set_ylabel('y')
# ax3d.set_zlabel('z')
# ax3d.plot_surface(x,y,z,rstride=30,cstride=30,cmap='jet')
# mp.show()

fig = mp.figure('3d',facecolor='gray')
ax3d = fig.add_subplot(111,projection='3d')
ax3d.set_xlabel('x')
ax3d.set_ylabel('y')
ax3d.set_zlabel('z')
ax3d.plot_surface(x,y,z,rstride=10,cstride=1,cmap='jet')
mp.show()

# x = np.random.normal(0,1,n)
# y = np.random.normal(0,1,n)
# z = np.random.normal(0,1,n)
# print(x)
# fig = mp.figure('test')
# # ax3d = mp.gca(projection='3d')
# # ax3d = mp.gca(projection='3d')
# ax3d = fig.add_subplot(3,3,1, projection='3d')
# ax3d.set_xlabel('x')
# ax3d.set_ylabel('y')
# ax3d.set_zlabel('z')
# d = x**2+y**2+z**2
# ax3d.scatter(x,y,z,s=3,marker='s',c=d,cmap='jet')

# ax3dss = fig.add_subplot(3,3,3, projection='3d')
# ax3dss.set_xlabel('x')
# ax3dss.set_ylabel('y')
# ax3dss.set_zlabel('z')
# f = x**2+y**2+z**2
# ax3dss.scatter(x,y,z,s=3,marker='o',c=f,cmap='Reds')
# mp.show()

#生成网格化坐标矩阵
# x,y=np.meshgrid(np.linspace(-3,3,n),
#                 np.linspace(-3,3,n))
# # print(x,y)
# # #根据每个网格点坐标，通过某个公式计算z高度坐标
# z=(1 - x/2 + x**5 + y**3)* np.exp(-x**2 - y**2)
# mp.imshow(z,cmap="jet")
# mp.colorbar()
# mp.show()

n=1000
#生成网格化坐标矩阵
x,y=np.meshgrid(np.linspace(-3,3,n),
                np.linspace(-3,3,n))
# print(x,y)
# #根据每个网格点坐标，通过某个公式计算z高度坐标
z=(1 - x/2 + x**5 + y**3)* np.exp(-x**2 - y**2)
# mp.figure('contour', facecolor='lightgray')
# mp.title('contour', fontsize=20)
# mp.xlabel('x', fontsize=14)
# mp.ylabel('y', fontsize=14)
# mp.tick_params(labelsize=20)
# mp.grid(linestyle=':')

# # 绘制等高线图
# mp.contourf(x,y,z,8,cmap='jet')
# cntr = mp.contour(x,y,z,8, colors='red',
#                   linewidths=2)
# mp.scatter([-2],[-2], marker='o', s=20, color='green')

# mp.show()




# numlist = np.arange(1,10,1)
# # print(numlist)
# mp.figure('title',facecolor='gray')
# for i,locator in enumerate(numlist):

#     mp.subplot(3,3,i+1)
#     mp.xlim(0,i+1)
#     mp.yticks([])
#     mp.xlabel('xxx')
#     ax = mp.gca()
#     ax.spines['right'].set_color(None)
#     ax.spines['top'].set_color(None)
#     ax.xaxis.set_major_locator(mp.MultipleLocator(1))
#     ax.xaxis.set_minor_locator(mp.MultipleLocator(0.1))
    
#     if(i<5):
#         x = np.arange(0,i+10,1)
#         y = np.sin(x)
#         mp.plot(x, y, '--', linewidth=0.5, color='red')  # 修正参数名
#     else:
#         if i>7:
#             values = [11,22,33,44,55]
#             space = [0.05,0.06,0.01,0.01,0.01]
#             labels = ['aa','bb','cc','dd','ee']
#             colos = ['dodgerblue','orangered','green','violet','gold']
#             mp.pie(values, space,labels,colos,shadow=True,startangle=20,radius=1)
#         else:
#             x = np.random.normal(i,i/2,3)
#             y = x+1
#             mp.bar(x,y,width=i/10,color="red",label='test')
#     # mp.plot(x,y,':',width='0.1',color='red')
# mp.tight_layout()
# mp.legend()
# mp.show()


# values = [11,22,33,44,55]
# space = [0.05,0.06,0.01,0.01,0.01]
# labels = ['aa','bb','cc','dd','ee']
# colos = ['dodgerblue','orangered','green','violet','gold']
# mp.figure('tesgt')
# # mp.axis('equal')
# mp.pie(values,space,labels,colos,'%f%%')
# mp.legend()
# mp.show()

# 条形
# apples = np.array([22,333,55,666,777])
# oranges = np.array([67,23,77,54,64])

# mp.figure('test',facecolor='gray')
# mp.title('title')
# mp.grid(linestyle=':')
# x = np.arange(apples.size)
# mp.bar(x-0.2,apples,width=0.4,color='red',label='apples')
# mp.bar(x+0.2,oranges,width=0.4,color='blue',label='oranges')
# mp.xticks(x,['j','f','m','a','m'])
# mp.legend()
# mp.show()


# 折线
# x = np.linspace(0,8*np.pi,1000)
# sinx = np.sin(x)
# cosx = np.cos(x)
# mp.figure('test',facecolor='lightgray');
# mp.title('title')
# mp.grid(linestyle=":")
# mp.plot(x,sinx,color="blue",label=r'$y=sin(x)$')
# mp.plot(x,cosx,color="orangered",label=r'$y=\frac{1}{2}cos(\frac{x}{2})$')

# mp.fill_between(x,sinx,cosx,sinx>cosx,color="yellow")
# mp.fill_between(x,sinx,cosx,sinx<cosx,color="orangered")

# mp.legend()
# mp.show()

# mp.subplot(3,3,1)
# # ax = mp.gca()
# mp.xlim(1,10)
# mp.ylim(1,300)
# mp.grid(linestyle='--')
# ax.xaxis.set_major_locator(mp.MultipleLocator(1))
# ax.xaxis.set_minor_locator(mp.MultipleLocator(0.1))

# ax.yaxis.set_major_locator(mp.MultipleLocator(50))
# ax.yaxis.set_minor_locator(mp.MultipleLocator(5))

# ax.grid(which='major',axis="both",color="red", linewidth=0.75)
# ax.grid(which='minor',axis="both",color="blue", linewidth=0.25)

# y = [1,10,100,1000,100,10,1]
# mp.plot(y,'o-',color="dodgerblue")
# mp.show()







# locators = ['mp.NullLocator()','mp.MaxNLocator(nbins=4)','mp.FixedLocator([3,6,9])']
# mp.figure('test',facecolor='lightgray');

# for i,locator in enumerate(locators):
#     # lenght()
#     mp.subplot(len(locators),1,i+1);
#     mp.xlim(1,10)
#     mp.yticks([])

#     ax = mp.gca()
#     ax.spines['top'].set_color('none');
#     ax.spines['right'].set_color('none');
#     ax.spines['left'].set_color('none');
#     ax.spines['bottom'].set_position(('data',0.5))
#     tlocator = eval(locator)
#     ax.xaxis.set_major_locator(tlocator)
#     ax.xaxis.set_minor_locator(mp.MultipleLocator(0.1))



# mp.figure('test',facecolor='lightgray')

# for i in range(1,10):
#     mp.subplot(3,3,i)
#     mp.text(0.5,0.5,i,ha='center',va='center',size=30,alpha=0.5)
#     mp.xticks([])
#     mp.yticks([])


# mp.tight_layout()
# mp.show()



# mp.figure('test',figsize=(4,3),facecolor='gray')
# mp.plot([1,2,3,4],[11,22,33,44])
# # spline = mp.gca()
# # mp.
# mp.xlabel('xxxx',fontsize=15)
# mp.ylabel('yyy',fontsize=15)
# mp.title('aaaa')
# mp.grid(linestyle="dotted")
# mp.show()



# a = np.array([2,3,4,7])
# b = np.array([9,10,22,33])
# c =np.vstack((a,b))
# d = np.column_stack((a,b))
# print(c,d)

a = np.array([2,3,4,7])
b = np.array([1,2,4])

# 补  头 尾 常量 值
c = np.pad(b,pad_width=(0,1),mode='constant',constant_values=-1)
print(c)
# a = np.arange(1,7).reshape(2,3)
# b = np.arange(7,13).reshape(2,3)
# c = np.vstack((a,b))
# d,e,f,g = np.vsplit(c,4)
# print(c,d,e,f,g)


# concatenate((a,b),axis = 0)
# split(c,2,axis = 0)

a = np.arange(1,7).reshape(2,3)
b = np.arange(7,13).reshape(2,3)
c = np.dstack((a,b))
# h, i, j = np.hsplit(c,3)
# print(c)


# a = np.arange(1,7).reshape(2,3)
# b = np.arange(7,13).reshape(2,3)
# c = np.hstack((a,b))
# h, i, j = np.hsplit(c,3)
# print(c)

# d,e,f = np.hsplit(c,2)
# print(c,h, i, j)



# print(d)
# vstack  垂直
# vsplit

# hstack
# hsplit


# dstack
# daplit

# shape = 
# resize()

# reshage()
# ravel()

# flatten()

# c = a%3 == 0 & a%6 == 0
# print(c)
# print(a[c])
# a.resize(3,3)
# a.shape=(9,)

# b = a.reshape(9,)
# c = b.ravel()
# a[1][1] = 123
# print(a)
# print(c)
# b = a.flatten()
# a[1] = 5
# print (a)
# print (b)

dates = ['2020-01-01','2020-01-01 02:19:22']
data = np.array(dates)
data = data.astype('M8[s]')
# print(data,data.dtype)

# data = [(1,['aa','bb','cc'],11),(2,['dd','ee','ff'],44)]
# a = np.array(data,dtype={
#     'names':['num','detail','age'],
#     'formats':['i2','3U2','i2']})
# print(a[1]['age'])
