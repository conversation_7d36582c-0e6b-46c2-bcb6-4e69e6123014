#!/usr/bin/env python
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fmb_Django.settings')
django.setup()

from coupons.models import Coupon
from django.utils import timezone

def create_test_coupons():
    """创建测试优惠券数据"""
    
    # 清除现有的测试数据
    Coupon.objects.filter(name__contains='测试').delete()
    
    now = timezone.now()
    
    # 创建测试优惠券
    test_coupons = [
        {
            'name': '新用户专享优惠券',
            'amount': 20.00,
            'min_amount': 100.00,
            'start_time': now,
            'end_time': now + timedelta(days=30),
            'description': '新用户注册专享，满100元可用',
            'coupon_code': 'NEW_USER_20',
            'status': 'active'
        },
        {
            'name': '满减优惠券50元',
            'amount': 50.00,
            'min_amount': 200.00,
            'start_time': now,
            'end_time': now + timedelta(days=15),
            'description': '满200元减50元，限时优惠',
            'coupon_code': 'SAVE_50_200',
            'status': 'active'
        },
        {
            'name': '无门槛优惠券',
            'amount': 10.00,
            'min_amount': 0.00,
            'start_time': now,
            'end_time': now + timedelta(days=7),
            'description': '无门槛使用，任意金额可用',
            'coupon_code': 'FREE_10',
            'status': 'active'
        },
        {
            'name': '已过期优惠券',
            'amount': 30.00,
            'min_amount': 150.00,
            'start_time': now - timedelta(days=10),
            'end_time': now - timedelta(days=1),
            'description': '这是一个已过期的优惠券',
            'coupon_code': 'EXPIRED_30',
            'status': 'expired'
        },
        {
            'name': '已使用优惠券',
            'amount': 25.00,
            'min_amount': 80.00,
            'start_time': now - timedelta(days=5),
            'end_time': now + timedelta(days=10),
            'description': '这是一个已使用的优惠券',
            'coupon_code': 'USED_25',
            'status': 'used',
            'is_used': True,
            'used_at': now - timedelta(days=2),
            'order_id': 12345
        },
        {
            'name': '大额优惠券',
            'amount': 100.00,
            'min_amount': 500.00,
            'start_time': now,
            'end_time': now + timedelta(days=60),
            'description': '大额购物专享，满500元可用',
            'coupon_code': 'BIG_100_500',
            'status': 'active'
        }
    ]
    
    created_count = 0
    for coupon_data in test_coupons:
        coupon = Coupon.objects.create(**coupon_data)
        print(f"创建优惠券: {coupon.name} (ID: {coupon.id})")
        created_count += 1
    
    print(f"\n成功创建 {created_count} 个测试优惠券！")
    print("\n可以通过以下方式访问:")
    print("- 优惠券首页: http://localhost:8000/coupons/")
    print("- 优惠券列表: http://localhost:8000/coupons/list/")
    print("- API演示页面: http://localhost:8000/coupons/api-demo/")
    print("- Django管理后台: http://localhost:8000/admin/ (用户名: admin, 密码: admin123)")

if __name__ == '__main__':
    create_test_coupons()
