from django.urls import path
from . import views

app_name = 'goods'

urlpatterns = [
    # 页面路由
    path('', views.home, name='home'),
    path('list/', views.goods_list, name='list'),
    path('detail/<int:goods_id>/', views.goods_detail, name='detail'),
    path('create/', views.goods_create, name='create'),
    path('edit/<int:goods_id>/', views.goods_edit, name='edit'),
    path('delete/<int:goods_id>/', views.goods_delete, name='delete'),
    path('api-demo/', views.api_demo, name='api_demo'),
    
    # API 路由
    path('api/list/', views.api_goods_list, name='api_list'),
    path('api/list/optimized/', views.api_goods_list_optimized, name='api_list_optimized'),
    path('api/list/cursor/', views.api_goods_cursor_pagination, name='api_list_cursor'),
    path('api/detail/<int:goods_id>/', views.api_goods_detail, name='api_detail'),
    path('api/create/', views.api_goods_create, name='api_create'),
]
