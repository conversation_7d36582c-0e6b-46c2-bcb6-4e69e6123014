from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from .models import Goods
import json

def home(request):
    """首页 - 显示商品列表"""
    # 只获取最新的几个商品用于首页展示
    recent_goods = Goods.objects.order_by('-create_at')[:5]
    total_count = Goods.objects.count()

    return render(request, 'goods/index.html', {
        'recent_goods': recent_goods,
        'total_count': total_count
    })

def api_demo(request):
    """API 演示页面"""
    return render(request, 'goods/api_demo.html')

def goods_list(request):
    """商品列表页"""
    goods_list = Goods.objects.all().order_by('-create_at')
    
    # 搜索功能
    search = request.GET.get('search')
    if search:
        goods_list = goods_list.filter(name__icontains=search)
    
    # 分页
    paginator = Paginator(goods_list, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'goods/list.html', {
        'page_obj': page_obj,
        'search': search
    })

def goods_detail(request, goods_id):
    """商品详情页"""
    goods = get_object_or_404(Goods, id=goods_id)
    return render(request, 'goods/detail.html', {'goods': goods})

def goods_create(request):
    """创建商品"""
    if request.method == 'POST':
        name = request.POST.get('name')
        price = request.POST.get('price')
        stock = request.POST.get('stock')
        shopid = request.POST.get('shopid')
        max_price = request.POST.get('max_price')
        description = request.POST.get('description')
        
        goods = Goods.objects.create(
            name=name,
            price=float(price) if price else 0,
            stock=int(stock) if stock else 0,
            shopid=int(shopid) if shopid else None,
            max_price=float(max_price) if max_price else None,
            description=description or ''
        )
        
        return redirect('goods:detail', goods_id=goods.id)
    
    return render(request, 'goods/create.html')

def goods_edit(request, goods_id):
    """编辑商品"""
    goods = get_object_or_404(Goods, id=goods_id)
    
    if request.method == 'POST':
        goods.name = request.POST.get('name', goods.name)
        goods.price = float(request.POST.get('price', goods.price))
        goods.stock = int(request.POST.get('stock', goods.stock))
        goods.shopid = int(request.POST.get('shopid')) if request.POST.get('shopid') else goods.shopid
        goods.max_price = float(request.POST.get('max_price')) if request.POST.get('max_price') else goods.max_price
        goods.description = request.POST.get('description', goods.description)
        
        goods.save()
        return redirect('goods:detail', goods_id=goods.id)
    
    return render(request, 'goods/edit.html', {'goods': goods})

def goods_delete(request, goods_id):
    """删除商品"""
    goods = get_object_or_404(Goods, id=goods_id)
    
    if request.method == 'POST':
        goods.delete()
        return redirect('goods:list')
    
    return render(request, 'goods/delete.html', {'goods': goods})

# API 接口
@csrf_exempt
def api_goods_list(request):
    """API: 获取商品列表 - 支持分页和搜索"""
    # 获取查询参数
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    search = request.GET.get('search', '')

    # 构建查询集
    queryset = Goods.objects.all().order_by('-create_at')

    # 搜索过滤
    if search:
        queryset = queryset.filter(name__icontains=search)

    # 分页处理
    paginator = Paginator(queryset, page_size)

    try:
        page_obj = paginator.page(page)
    except:
        page_obj = paginator.page(1)

    # 获取当前页数据
    goods_list = page_obj.object_list.values(
        'id', 'name', 'price', 'stock', 'shopid', 'max_price', 'description', 'create_at'
    )

    # 返回分页信息
    return JsonResponse({
        'data': list(goods_list),
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_count': paginator.count,
            'page_size': page_size,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
        }
    })

@csrf_exempt
def api_goods_detail(request, goods_id):
    """API: 获取单个商品详情"""
    try:
        goods = Goods.objects.get(id=goods_id)
        data = {
            'id': goods.id,
            'name': goods.name,
            'price': goods.price,
            'stock': goods.stock,
            'shopid': goods.shopid,
            'max_price': float(goods.max_price) if goods.max_price else None,
            'description': goods.description,
            'create_at': goods.create_at.isoformat(),
            'update_at': goods.update_at.isoformat(),
        }
        return JsonResponse(data)
    except Goods.DoesNotExist:
        return JsonResponse({'error': '商品不存在'}, status=404)

@csrf_exempt
def api_goods_create(request):
    """API: 创建商品"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            goods = Goods.objects.create(
                name=data.get('name', ''),
                price=data.get('price', 0),
                stock=data.get('stock', 0),
                shopid=data.get('shopid'),
                max_price=data.get('max_price'),
                description=data.get('description', '')
            )
            return JsonResponse({
                'id': goods.id,
                'message': '商品创建成功'
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)
    
    return JsonResponse({'error': '只支持POST请求'}, status=405)

@csrf_exempt
def api_goods_list_optimized(request):
    """API: 优化的商品列表查询 - 演示不同的分页策略"""
    # 获取查询参数
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    search = request.GET.get('search', '')
    order_by = request.GET.get('order_by', '-create_at')  # 排序字段

    # 基础查询集 - 只选择需要的字段，避免查询所有字段
    queryset = Goods.objects.only(
        'id', 'name', 'price', 'stock', 'shopid', 'max_price', 'create_at'
    ).order_by(order_by)

    # 搜索过滤
    if search:
        queryset = queryset.filter(name__icontains=search)

    # 计算偏移量 - 对于大数据量，这比 Paginator 更高效
    offset = (page - 1) * page_size
    limit = page_size

    # 获取总数（只在第一页时计算，减少数据库查询）
    if page == 1:
        total_count = queryset.count()
    else:
        # 对于后续页面，可以从缓存或其他方式获取总数
        total_count = None

    # 使用 offset 和 limit 进行分页查询
    goods_list = list(queryset[offset:offset + limit].values(
        'id', 'name', 'price', 'stock', 'shopid', 'max_price', 'create_at'
    ))

    # 检查是否还有下一页
    has_next = len(goods_list) == page_size
    if has_next:
        # 检查下一页是否真的有数据
        next_page_exists = queryset[offset + limit:offset + limit + 1].exists()
        has_next = next_page_exists

    return JsonResponse({
        'data': goods_list,
        'pagination': {
            'current_page': page,
            'page_size': page_size,
            'has_next': has_next,
            'has_previous': page > 1,
            'total_count': total_count,  # 可能为 None
        },
        'query_info': {
            'search': search,
            'order_by': order_by,
            'optimization': 'offset_limit'
        }
    })

@csrf_exempt
def api_goods_cursor_pagination(request):
    """API: 基于游标的分页 - 适合大数据量的实时数据"""
    # 获取查询参数
    cursor = request.GET.get('cursor')  # 上一页的最后一个ID
    page_size = int(request.GET.get('page_size', 10))
    search = request.GET.get('search', '')

    # 基础查询集
    queryset = Goods.objects.only(
        'id', 'name', 'price', 'stock', 'create_at'
    ).order_by('-id')  # 按ID倒序，确保稳定排序

    # 搜索过滤
    if search:
        queryset = queryset.filter(name__icontains=search)

    # 游标分页
    if cursor:
        queryset = queryset.filter(id__lt=int(cursor))

    # 获取数据（多取一个用于判断是否有下一页）
    goods_list = list(queryset[:page_size + 1].values(
        'id', 'name', 'price', 'stock', 'create_at'
    ))

    # 判断是否有下一页
    has_next = len(goods_list) > page_size
    if has_next:
        goods_list = goods_list[:-1]  # 移除多取的那一个

    # 获取下一页的游标
    next_cursor = goods_list[-1]['id'] if goods_list else None

    return JsonResponse({
        'data': goods_list,
        'pagination': {
            'next_cursor': next_cursor,
            'has_next': has_next,
            'page_size': page_size,
        },
        'query_info': {
            'search': search,
            'current_cursor': cursor,
            'optimization': 'cursor_based'
        }
    })

# Create your views here.
