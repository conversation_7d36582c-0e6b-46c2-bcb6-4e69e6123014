from django.db import models

class Goods(models.Model):
    id = models.AutoField(primary_key=True, verbose_name="商品id")
    name = models.CharField(max_length=100, blank=True, verbose_name="商品名称")
    shopid = models.IntegerField(blank=True, null=True, verbose_name="店铺id")  # 移除max_length
    price = models.FloatField(verbose_name="价格")
    max_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="最高价格")
    stock = models.IntegerField(verbose_name="库存")
    create_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    description = models.TextField(blank=True, verbose_name="商品描述")

    class Meta:
        app_label = 'goods'
        verbose_name = "商品"
        verbose_name_plural = "商品"
        db_table = "goods"

    def __str__(self):
        return self.name or f"商品{self.id}"
