{% extends 'goods/base.html' %}

{% block title %}商品列表{% endblock %}

{% block content %}
<h1>商品列表</h1>

<!-- 搜索框 -->
<div style="margin-bottom: 20px;">
    <form method="get">
        <input type="text" name="search" value="{{ search }}" placeholder="搜索商品名称...">
        <button type="submit">搜索</button>
        <a href="{% url 'goods:create' %}" style="margin-left: 20px;">添加新商品</a>
    </form>
</div>

<table border="1" style="width: 100%; border-collapse: collapse;">
    <thead>
        <tr style="background-color: #f0f0f0;">
            <th style="padding: 10px;">ID</th>
            <th style="padding: 10px;">商品名称</th>
            <th style="padding: 10px;">价格</th>
            <th style="padding: 10px;">最高价格</th>
            <th style="padding: 10px;">库存</th>
            <th style="padding: 10px;">店铺ID</th>
            <th style="padding: 10px;">创建时间</th>
            <th style="padding: 10px;">操作</th>
        </tr>
    </thead>
    <tbody>
        {% for goods in page_obj %}
        <tr>
            <td style="padding: 8px;">{{ goods.id }}</td>
            <td style="padding: 8px;">{{ goods.name|default:"未命名" }}</td>
            <td style="padding: 8px;">￥{{ goods.price }}</td>
            <td style="padding: 8px;">
                {% if goods.max_price %}
                    ￥{{ goods.max_price }}
                {% else %}
                    -
                {% endif %}
            </td>
            <td style="padding: 8px;">{{ goods.stock }}</td>
            <td style="padding: 8px;">{{ goods.shopid|default:"-" }}</td>
            <td style="padding: 8px;">{{ goods.create_at|date:"Y-m-d H:i" }}</td>
            <td style="padding: 8px;">
                <a href="{% url 'goods:detail' goods.id %}">查看</a> |
                <a href="{% url 'goods:edit' goods.id %}">编辑</a> |
                <a href="{% url 'goods:delete' goods.id %}" onclick="return confirm('确定要删除这个商品吗？')">删除</a>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" style="padding: 20px; text-align: center;">
                {% if search %}
                    没有找到包含 "{{ search }}" 的商品
                {% else %}
                    暂无商品数据
                {% endif %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- 分页 -->
{% if page_obj.has_other_pages %}
<div style="margin-top: 20px; text-align: center;">
    {% if page_obj.has_previous %}
        <a href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">&laquo; 上一页</a>
    {% endif %}
    
    <span style="margin: 0 20px;">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
    
    {% if page_obj.has_next %}
        <a href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}">下一页 &raquo;</a>
    {% endif %}
</div>
{% endif %}

<div style="margin-top: 20px;">
    <a href="{% url 'goods:home' %}">返回首页</a>
</div>
{% endblock %}
