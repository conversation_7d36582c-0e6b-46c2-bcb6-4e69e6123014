{% extends 'goods/base.html' %}

{% block title %}删除商品 - {{ goods.name }}{% endblock %}

{% block content %}
<h1>删除商品</h1>

<div style="max-width: 600px; margin: 0 auto; text-align: center;">
    <div style="background-color: #ffebee; border: 1px solid #f44336; padding: 20px; margin-bottom: 20px;">
        <h2 style="color: #d32f2f;">⚠️ 警告</h2>
        <p>您确定要删除以下商品吗？此操作不可撤销！</p>
    </div>

    <div style="background-color: #f5f5f5; padding: 20px; margin-bottom: 20px; text-align: left;">
        <h3>商品信息：</h3>
        <p><strong>商品名称：</strong>{{ goods.name|default:"未命名" }}</p>
        <p><strong>价格：</strong>￥{{ goods.price }}</p>
        <p><strong>库存：</strong>{{ goods.stock }}</p>
        <p><strong>创建时间：</strong>{{ goods.create_at|date:"Y年m月d日 H:i:s" }}</p>
        {% if goods.description %}
        <p><strong>描述：</strong>{{ goods.description|truncatewords:20 }}</p>
        {% endif %}
    </div>

    <form method="post" style="display: inline;">
        {% csrf_token %}
        <button type="submit" 
                style="padding: 10px 20px; background-color: #f44336; color: white; border: none; cursor: pointer; margin-right: 10px;"
                onclick="return confirm('最后确认：真的要删除这个商品吗？')">
            确认删除
        </button>
    </form>
    
    <a href="{% url 'goods:detail' goods.id %}" 
       style="padding: 10px 20px; background-color: #ccc; color: black; text-decoration: none; display: inline-block;">
        取消
    </a>
</div>

<div style="margin-top: 20px; text-align: center;">
    <a href="{% url 'goods:detail' goods.id %}">返回详情</a> |
    <a href="{% url 'goods:list' %}">返回列表</a> |
    <a href="{% url 'goods:home' %}">返回首页</a>
</div>
{% endblock %}
