{% extends 'goods/base.html' %}

{% block title %}添加商品{% endblock %}

{% block content %}
<h1>添加商品</h1>

<form method="post">
    {% csrf_token %}
    <div class="form-group">
        <label for="name">商品名称:</label>
        <input type="text" id="name" name="name" required>
    </div>
    
    <div class="form-group">
        <label for="price">价格:</label>
        <input type="number" id="price" name="price" step="0.01" required>
    </div>
    
    <div class="form-group">
        <label for="max_price">最高价格:</label>
        <input type="number" id="max_price" name="max_price" step="0.01">
    </div>
    
    <div class="form-group">
        <label for="stock">库存:</label>
        <input type="number" id="stock" name="stock" required>
    </div>
    
    <div class="form-group">
        <label for="shopid">店铺ID:</label>
        <input type="number" id="shopid" name="shopid">
    </div>
    
    <div class="form-group">
        <label for="description">商品描述:</label>
        <textarea id="description" name="description" rows="4"></textarea>
    </div>
    
    <button type="submit" class="btn">保存</button>
    <a href="{% url 'goods:list' %}" class="btn" style="background: #6c757d;">取消</a>
</form>
{% endblock %}