{% extends 'goods/base.html' %}

{% block title %}商品首页{% endblock %}

{% block content %}
<h1>商品管理系统</h1>
<p>欢迎使用商品管理系统！</p>

<div style="background-color: #f8f9fa; padding: 20px; margin-bottom: 20px; border-radius: 5px;">
    <h2>📊 商品统计</h2>
    <p>总商品数量: <strong>{{ total_count }}</strong></p>
</div>

<h2>🆕 最新商品 (最近5个)</h2>
{% for goods in recent_goods %}
<div style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 5px;">
    <h3><a href="{% url 'goods:detail' goods.id %}" style="color: #007bff; text-decoration: none;">{{ goods.name|default:"未命名商品" }}</a></h3>
    <p><strong>价格:</strong> ￥{{ goods.price }}</p>
    {% if goods.max_price %}
    <p><strong>最高价格:</strong> ￥{{ goods.max_price }}</p>
    {% endif %}
    <p><strong>库存:</strong> {{ goods.stock }}</p>
    {% if goods.shopid %}
    <p><strong>店铺ID:</strong> {{ goods.shopid }}</p>
    {% endif %}
    <p><strong>创建时间:</strong> {{ goods.create_at|date:"Y-m-d H:i" }}</p>
    {% if goods.description %}
    <p><strong>描述:</strong> {{ goods.description|truncatewords:15 }}</p>
    {% endif %}
</div>
{% empty %}
<div style="text-align: center; padding: 40px; background-color: #f8f9fa; border-radius: 5px;">
    <p>暂无商品数据</p>
    <a href="{% url 'goods:create' %}" class="btn">添加第一个商品</a>
</div>
{% endfor %}

{% if recent_goods %}
<div style="text-align: center; margin-top: 20px;">
    <a href="{% url 'goods:list' %}" class="btn">查看所有商品 →</a>
</div>
{% endif %}
{% endblock %}