{% extends 'goods/base.html' %}

{% block title %}编辑商品 - {{ goods.name }}{% endblock %}

{% block content %}
<h1>编辑商品</h1>

<div style="max-width: 600px; margin: 0 auto;">
    <form method="post">
        {% csrf_token %}
        
        <table style="width: 100%;">
            <tr>
                <td style="padding: 10px; width: 30%;"><label for="name"><strong>商品名称：</strong></label></td>
                <td style="padding: 10px;">
                    <input type="text" id="name" name="name" value="{{ goods.name }}" style="width: 100%; padding: 5px;" required>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px;"><label for="price"><strong>价格：</strong></label></td>
                <td style="padding: 10px;">
                    <input type="number" id="price" name="price" value="{{ goods.price }}" step="0.01" min="0" style="width: 100%; padding: 5px;" required>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px;"><label for="max_price"><strong>最高价格：</strong></label></td>
                <td style="padding: 10px;">
                    <input type="number" id="max_price" name="max_price" value="{{ goods.max_price }}" step="0.01" min="0" style="width: 100%; padding: 5px;">
                    <small>可选</small>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px;"><label for="stock"><strong>库存：</strong></label></td>
                <td style="padding: 10px;">
                    <input type="number" id="stock" name="stock" value="{{ goods.stock }}" min="0" style="width: 100%; padding: 5px;" required>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px;"><label for="shopid"><strong>店铺ID：</strong></label></td>
                <td style="padding: 10px;">
                    <input type="number" id="shopid" name="shopid" value="{{ goods.shopid }}" min="1" style="width: 100%; padding: 5px;">
                    <small>可选</small>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px; vertical-align: top;"><label for="description"><strong>商品描述：</strong></label></td>
                <td style="padding: 10px;">
                    <textarea id="description" name="description" rows="5" style="width: 100%; padding: 5px;">{{ goods.description }}</textarea>
                    <small>可选</small>
                </td>
            </tr>
        </table>

        <div style="margin-top: 20px; text-align: center;">
            <button type="submit" style="padding: 10px 20px; background-color: #007cba; color: white; border: none; cursor: pointer;">保存修改</button>
            <a href="{% url 'goods:detail' goods.id %}" style="margin-left: 10px; padding: 10px 20px; background-color: #ccc; color: black; text-decoration: none; display: inline-block;">取消</a>
        </div>
    </form>
</div>

<div style="margin-top: 20px; text-align: center;">
    <a href="{% url 'goods:detail' goods.id %}">返回详情</a> |
    <a href="{% url 'goods:list' %}">返回列表</a> |
    <a href="{% url 'goods:home' %}">返回首页</a>
</div>
{% endblock %}
