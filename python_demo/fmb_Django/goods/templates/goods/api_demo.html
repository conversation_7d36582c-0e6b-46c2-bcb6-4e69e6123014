{% extends 'goods/base.html' %}

{% block title %}API 演示{% endblock %}

{% block content %}
<h1>商品 API 演示</h1>

<div style="margin-bottom: 20px;">
    <h2>API 端点列表</h2>
    <ul>
        <li><strong>商品列表 (传统分页):</strong> <code>{% url 'goods:api_list' %}</code></li>
        <li><strong>商品列表 (优化分页):</strong> <code>{% url 'goods:api_list_optimized' %}</code></li>
        <li><strong>商品列表 (游标分页):</strong> <code>{% url 'goods:api_list_cursor' %}</code></li>
        <li><strong>商品详情:</strong> <code>{% url 'goods:api_detail' 1 %}</code></li>
        <li><strong>创建商品:</strong> <code>{% url 'goods:api_create' %}</code></li>
    </ul>
</div>

<div style="margin-bottom: 20px;">
    <h2>测试 API</h2>
    <button onclick="testApi('{% url 'goods:api_list' %}', 'result1')">测试传统分页</button>
    <button onclick="testApi('{% url 'goods:api_list_optimized' %}?page_size=3', 'result2')">测试优化分页</button>
    <button onclick="testApi('{% url 'goods:api_list_cursor' %}?page_size=2', 'result3')">测试游标分页</button>
</div>

<div id="result1" style="margin-bottom: 20px;"></div>
<div id="result2" style="margin-bottom: 20px;"></div>
<div id="result3" style="margin-bottom: 20px;"></div>

<script>
async function testApi(url, resultId) {
    const resultDiv = document.getElementById(resultId);
    resultDiv.innerHTML = '<p>正在加载...</p>';
    
    try {
        const response = await fetch(url);
        const data = await response.json();
        
        resultDiv.innerHTML = `
            <h3>API 结果 (${url}):</h3>
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto;">
${JSON.stringify(data, null, 2)}
            </pre>
        `;
    } catch (error) {
        resultDiv.innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
    }
}

// 页面加载时自动测试第一个 API
document.addEventListener('DOMContentLoaded', function() {
    testApi('{% url 'goods:api_list' %}?page_size=2', 'result1');
});
</script>

<div style="margin-top: 20px;">
    <a href="{% url 'goods:home' %}">返回首页</a>
</div>
{% endblock %}
