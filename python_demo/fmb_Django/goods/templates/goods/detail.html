{% extends 'goods/base.html' %}

{% block title %}商品详情 - {{ goods.name }}{% endblock %}

{% block content %}
<h1>商品详情</h1>

<div style="max-width: 600px; margin: 0 auto;">
    <table border="1" style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0; width: 30%;"><strong>商品ID</strong></td>
            <td style="padding: 10px;">{{ goods.id }}</td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>商品名称</strong></td>
            <td style="padding: 10px;">{{ goods.name|default:"未命名" }}</td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>价格</strong></td>
            <td style="padding: 10px;">￥{{ goods.price }}</td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>最高价格</strong></td>
            <td style="padding: 10px;">
                {% if goods.max_price %}
                    ￥{{ goods.max_price }}
                {% else %}
                    未设置
                {% endif %}
            </td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>库存</strong></td>
            <td style="padding: 10px;">{{ goods.stock }}</td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>店铺ID</strong></td>
            <td style="padding: 10px;">{{ goods.shopid|default:"未设置" }}</td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>商品描述</strong></td>
            <td style="padding: 10px;">
                {% if goods.description %}
                    {{ goods.description|linebreaks }}
                {% else %}
                    暂无描述
                {% endif %}
            </td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>创建时间</strong></td>
            <td style="padding: 10px;">{{ goods.create_at|date:"Y年m月d日 H:i:s" }}</td>
        </tr>
        <tr>
            <td style="padding: 10px; background-color: #f0f0f0;"><strong>更新时间</strong></td>
            <td style="padding: 10px;">{{ goods.update_at|date:"Y年m月d日 H:i:s" }}</td>
        </tr>
    </table>

    <div style="margin-top: 20px; text-align: center;">
        <a href="{% url 'goods:edit' goods.id %}" style="margin-right: 10px;">编辑商品</a>
        <a href="{% url 'goods:delete' goods.id %}" onclick="return confirm('确定要删除这个商品吗？')" style="margin-right: 10px; color: red;">删除商品</a>
        <a href="{% url 'goods:list' %}" style="margin-right: 10px;">返回列表</a>
        <a href="{% url 'goods:home' %}">返回首页</a>
    </div>
</div>
{% endblock %}
