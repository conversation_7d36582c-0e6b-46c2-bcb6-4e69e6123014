from django.contrib import admin
from .models import Goods

@admin.register(Goods)
class GoodsAdmin(admin.ModelAdmin):
    """商品模型的Admin配置"""
    # 列表页显示的字段
    list_display = ('id', 'name', 'price', 'stock', 'shopid', 'create_at')
    # 可搜索的字段
    search_fields = ('name', 'description')
    # 过滤条件
    list_filter = ('create_at', 'shopid', 'id', 'name')
    # 只读字段
    readonly_fields = ('create_at', 'update_at')
    # 编辑页的字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'shopid')
        }),
        ('价格与库存', {
            'fields': ('price', 'max_price', 'stock')
        }),
        ('时间信息', {
            'fields': ('create_at', 'update_at'),
            'classes': ('collapse',)  # 默认折叠
        }),
    )
    # 每页显示数量
    list_per_page = 2
    # 排序
    ordering = ['-create_at']
# Register your models here.
