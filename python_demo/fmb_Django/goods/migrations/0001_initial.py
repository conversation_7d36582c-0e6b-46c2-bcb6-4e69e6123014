# Generated by Django 5.2.4 on 2025-07-29 06:32

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Goods',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='商品id')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='商品名称')),
                ('shopid', models.IntegerField(blank=True, null=True, verbose_name='店铺id')),
                ('price', models.FloatField(verbose_name='价格')),
                ('max_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='最高价格')),
                ('stock', models.IntegerField(verbose_name='库存')),
                ('create_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('description', models.TextField(blank=True, verbose_name='商品描述')),
            ],
            options={
                'verbose_name': '商品',
                'verbose_name_plural': '商品',
                'db_table': 'goods',
            },
        ),
    ]
