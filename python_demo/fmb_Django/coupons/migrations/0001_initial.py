# Generated by Django 5.2.4 on 2025-07-29 09:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Coupon",
            fields=[
                (
                    "id",
                    models.AutoField(
                        primary_key=True, serialize=False, verbose_name="优惠券ID"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="优惠名称")),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="优惠金额"
                    ),
                ),
                (
                    "min_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=10,
                        verbose_name="最少使用额度",
                    ),
                ),
                (
                    "order_id",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="使用的订单ID"
                    ),
                ),
                ("start_time", models.DateTimeField(verbose_name="有效期开始时间")),
                ("end_time", models.DateTimeField(verbose_name="有效期结束时间")),
                (
                    "create_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "used_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="使用时间"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "有效"),
                            ("used", "已使用"),
                            ("expired", "已过期"),
                            ("disabled", "已禁用"),
                        ],
                        default="active",
                        max_length=20,
                        verbose_name="优惠券状态",
                    ),
                ),
                (
                    "is_used",
                    models.BooleanField(default=False, verbose_name="是否已使用"),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="优惠券描述"),
                ),
                (
                    "coupon_code",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="优惠券代码"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="使用的用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "优惠券",
                "verbose_name_plural": "优惠券",
                "db_table": "coupons",
                "ordering": ["-create_at"],
            },
        ),
    ]
