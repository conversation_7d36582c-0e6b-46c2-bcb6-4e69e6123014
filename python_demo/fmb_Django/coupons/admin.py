from django.contrib import admin
from .models import Coupon

@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    """优惠券模型的Admin配置"""
    # 列表页显示的字段
    list_display = ('id', 'name', 'amount', 'min_amount', 'coupon_code', 'status', 'is_used', 'start_time', 'end_time', 'create_at')
    # 可搜索的字段
    search_fields = ('name', 'coupon_code', 'description')
    # 过滤条件
    list_filter = ('status', 'is_used', 'create_at', 'start_time', 'end_time')
    # 只读字段
    readonly_fields = ('create_at', 'update_at', 'used_at')
    # 编辑页的字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'coupon_code')
        }),
        ('金额设置', {
            'fields': ('amount', 'min_amount')
        }),
        ('有效期设置', {
            'fields': ('start_time', 'end_time')
        }),
        ('状态信息', {
            'fields': ('status', 'is_used')
        }),
        ('使用信息', {
            'fields': ('user', 'order_id', 'used_at'),
            'classes': ('collapse',)  # 默认折叠
        }),
        ('时间信息', {
            'fields': ('create_at', 'update_at'),
            'classes': ('collapse',)  # 默认折叠
        }),
    )
    # 每页显示数量
    list_per_page = 20
    # 排序
    ordering = ['-create_at']
    # 日期层次结构
    date_hierarchy = 'create_at'
