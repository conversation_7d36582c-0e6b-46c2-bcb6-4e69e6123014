from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import models
from .models import Coupon
import json
import uuid

def home(request):
    """首页 - 显示优惠券列表"""
    # 只获取最新的几个优惠券用于首页展示
    recent_coupons = Coupon.objects.order_by('-create_at')[:5]
    total_count = Coupon.objects.count()
    active_count = Coupon.objects.filter(status='active', is_used=False).count()

    return render(request, 'coupons/index.html', {
        'recent_coupons': recent_coupons,
        'total_count': total_count,
        'active_count': active_count
    })

def api_demo(request):
    """API 演示页面"""
    return render(request, 'coupons/api_demo.html')

def coupon_list(request):
    """优惠券列表页"""
    # 基础查询 - 只查询需要的字段，提高性能
    coupon_list = Coupon.objects.select_related('user').only(
        'id', 'name', 'amount', 'min_amount', 'coupon_code', 'status',
        'is_used', 'start_time', 'end_time', 'create_at', 'user__username'
    ).order_by('-create_at')

    # 搜索功能 - 支持名称和优惠券代码搜索
    search = request.GET.get('search')
    if search:
        coupon_list = coupon_list.filter(
            models.Q(name__icontains=search) |
            models.Q(coupon_code__icontains=search)
        )

    # 状态过滤
    status = request.GET.get('status')
    if status:
        coupon_list = coupon_list.filter(status=status)

    # 是否已使用过滤
    is_used = request.GET.get('is_used')
    if is_used == '1':
        coupon_list = coupon_list.filter(is_used=True)
    elif is_used == '0':
        coupon_list = coupon_list.filter(is_used=False)

    # 金额范围过滤
    min_amount = request.GET.get('min_amount')
    max_amount = request.GET.get('max_amount')
    if min_amount:
        try:
            coupon_list = coupon_list.filter(amount__gte=float(min_amount))
        except ValueError:
            pass
    if max_amount:
        try:
            coupon_list = coupon_list.filter(amount__lte=float(max_amount))
        except ValueError:
            pass

    # 有效期过滤
    validity = request.GET.get('validity')
    now = timezone.now()
    if validity == 'valid':
        # 当前有效的优惠券
        coupon_list = coupon_list.filter(
            status='active',
            is_used=False,
            start_time__lte=now,
            end_time__gte=now
        )
    elif validity == 'expired':
        # 已过期的优惠券
        coupon_list = coupon_list.filter(end_time__lt=now)
    elif validity == 'future':
        # 未来生效的优惠券
        coupon_list = coupon_list.filter(start_time__gt=now)

    # 排序选项
    order_by = request.GET.get('order_by', '-create_at')
    valid_orders = ['-create_at', 'create_at', '-amount', 'amount', '-end_time', 'end_time', 'name']
    if order_by in valid_orders:
        coupon_list = coupon_list.order_by(order_by)

    # 分页 - 支持自定义每页数量
    page_size = request.GET.get('page_size', '10')
    try:
        page_size = int(page_size)
        if page_size not in [5, 10, 20, 50]:
            page_size = 10
    except ValueError:
        page_size = 10

    paginator = Paginator(coupon_list, page_size)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'coupons/list.html', {
        'page_obj': page_obj,
        'search': search,
        'status': status,
        'is_used': is_used,
        'min_amount': min_amount,
        'max_amount': max_amount,
        'validity': validity,
        'order_by': order_by,
        'page_size': page_size,
        'status_choices': Coupon.STATUS_CHOICES,
        'page_size_choices': [5, 10, 20, 50],
        'order_choices': [
            ('-create_at', '创建时间(新到旧)'),
            ('create_at', '创建时间(旧到新)'),
            ('-amount', '金额(高到低)'),
            ('amount', '金额(低到高)'),
            ('-end_time', '到期时间(晚到早)'),
            ('end_time', '到期时间(早到晚)'),
            ('name', '名称(A-Z)'),
        ]
    })

def coupon_detail(request, coupon_id):
    """优惠券详情页"""
    coupon = get_object_or_404(Coupon, id=coupon_id)
    return render(request, 'coupons/detail.html', {'coupon': coupon})

def coupon_create(request):
    """创建优惠券"""
    if request.method == 'POST':
        name = request.POST.get('name')
        amount = request.POST.get('amount')
        min_amount = request.POST.get('min_amount', 0)
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        description = request.POST.get('description', '')

        # 生成唯一的优惠券代码
        coupon_code = f"COUPON_{uuid.uuid4().hex[:8].upper()}"

        coupon = Coupon.objects.create(
            name=name,
            amount=float(amount) if amount else 0,
            min_amount=float(min_amount) if min_amount else 0,
            start_time=start_time,
            end_time=end_time,
            description=description,
            coupon_code=coupon_code
        )

        return redirect('coupons:detail', coupon_id=coupon.id)

    return render(request, 'coupons/create.html')

def coupon_edit(request, coupon_id):
    """编辑优惠券"""
    coupon = get_object_or_404(Coupon, id=coupon_id)

    if request.method == 'POST':
        coupon.name = request.POST.get('name', coupon.name)
        coupon.amount = float(request.POST.get('amount', coupon.amount))
        coupon.min_amount = float(request.POST.get('min_amount', coupon.min_amount))
        coupon.start_time = request.POST.get('start_time', coupon.start_time)
        coupon.end_time = request.POST.get('end_time', coupon.end_time)
        coupon.description = request.POST.get('description', coupon.description)
        coupon.status = request.POST.get('status', coupon.status)

        coupon.save()
        return redirect('coupons:detail', coupon_id=coupon.id)

    return render(request, 'coupons/edit.html', {
        'coupon': coupon,
        'status_choices': Coupon.STATUS_CHOICES
    })

def coupon_delete(request, coupon_id):
    """删除优惠券"""
    coupon = get_object_or_404(Coupon, id=coupon_id)

    if request.method == 'POST':
        coupon.delete()
        return redirect('coupons:list')

    return render(request, 'coupons/delete.html', {'coupon': coupon})

# API 接口
@csrf_exempt
def api_coupon_list(request):
    """API: 获取优惠券列表 - 支持分页和搜索"""
    # 获取查询参数
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')

    # 构建查询集
    queryset = Coupon.objects.all().order_by('-create_at')

    # 搜索过滤
    if search:
        queryset = queryset.filter(name__icontains=search)

    # 状态过滤
    if status:
        queryset = queryset.filter(status=status)

    # 分页处理
    paginator = Paginator(queryset, page_size)

    try:
        page_obj = paginator.page(page)
    except:
        page_obj = paginator.page(1)

    # 获取当前页数据
    coupon_list = []
    for coupon in page_obj.object_list:
        coupon_data = {
            'id': coupon.id,
            'name': coupon.name,
            'amount': float(coupon.amount),
            'min_amount': float(coupon.min_amount),
            'coupon_code': coupon.coupon_code,
            'status': coupon.status,
            'is_used': coupon.is_used,
            'start_time': coupon.start_time.isoformat(),
            'end_time': coupon.end_time.isoformat(),
            'create_at': coupon.create_at.isoformat(),
            'order_id': coupon.order_id,
            'user_id': coupon.user.id if coupon.user else None,
            'user_name': coupon.user.username if coupon.user else None,
            'is_valid': coupon.is_valid(),
        }
        coupon_list.append(coupon_data)

    # 返回分页信息
    return JsonResponse({
        'data': coupon_list,
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_count': paginator.count,
            'page_size': page_size,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
        }
    })

@csrf_exempt
def api_coupon_detail(request, coupon_id):
    """API: 获取单个优惠券详情"""
    try:
        coupon = Coupon.objects.get(id=coupon_id)
        data = {
            'id': coupon.id,
            'name': coupon.name,
            'amount': float(coupon.amount),
            'min_amount': float(coupon.min_amount),
            'coupon_code': coupon.coupon_code,
            'status': coupon.status,
            'is_used': coupon.is_used,
            'start_time': coupon.start_time.isoformat(),
            'end_time': coupon.end_time.isoformat(),
            'create_at': coupon.create_at.isoformat(),
            'update_at': coupon.update_at.isoformat(),
            'used_at': coupon.used_at.isoformat() if coupon.used_at else None,
            'order_id': coupon.order_id,
            'user_id': coupon.user.id if coupon.user else None,
            'user_name': coupon.user.username if coupon.user else None,
            'description': coupon.description,
            'is_valid': coupon.is_valid(),
        }
        return JsonResponse(data)
    except Coupon.DoesNotExist:
        return JsonResponse({'error': '优惠券不存在'}, status=404)

@csrf_exempt
def api_coupon_create(request):
    """API: 创建优惠券"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)

            # 生成唯一的优惠券代码
            coupon_code = data.get('coupon_code') or f"COUPON_{uuid.uuid4().hex[:8].upper()}"

            coupon = Coupon.objects.create(
                name=data.get('name', ''),
                amount=data.get('amount', 0),
                min_amount=data.get('min_amount', 0),
                start_time=data.get('start_time'),
                end_time=data.get('end_time'),
                description=data.get('description', ''),
                coupon_code=coupon_code
            )
            return JsonResponse({
                'id': coupon.id,
                'coupon_code': coupon.coupon_code,
                'message': '优惠券创建成功'
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)

    return JsonResponse({'error': '只支持POST请求'}, status=405)

# @csrf_exempt
def api_coupon_use(request, coupon_id):
    """API: 使用优惠券"""
    # if request.method != 'POST':
        # return JsonResponse({
        #     'error': '此API只支持POST请求',
        #     'message': '请使用POST方法并发送JSON数据'
        # }, status=405)

    assert False, f"Debug: request.body = {request.body}, method = {request.method}"
    # print('fdafa', request)  # 你的调试代码

    # if request.method == 'GET':
    #     try:
    #         print(request.body)
    #
    #         data = json.loads(request.body)
    #         coupon = Coupon.objects.get(id=coupon_id)
    #
    #         # 检查优惠券是否可用
    #         if not coupon.is_valid():
    #             return JsonResponse({'error': '优惠券不可用'}, status=400)
    #
    #         order_amount = data.get('order_amount', 0)
    #         if not coupon.can_use_for_amount(order_amount):
    #             return JsonResponse({'error': f'订单金额不足，最少需要{coupon.min_amount}元'}, status=400)
    #
    #         # 使用优惠券
    #         coupon.is_used = True
    #         coupon.status = 'used'
    #         coupon.used_at = timezone.now()
    #         coupon.order_id = data.get('order_id')
    #
    #         # 设置用户
    #         user_id = data.get('user_id')
    #         if user_id:
    #             try:
    #                 user = User.objects.get(id=user_id)
    #                 coupon.user = user
    #             except User.DoesNotExist:
    #                 pass
    #
    #         coupon.save()
    #
    #         return JsonResponse({
    #             'message': '优惠券使用成功',
    #             'discount_amount': float(coupon.amount)
    #         })
    #
    #     except Coupon.DoesNotExist:
    #         return JsonResponse({'error': '优惠券不存在'}, status=404)
    #     except Exception as e:
    #         return JsonResponse({'error': str(e)}, status=400)
    #
    return JsonResponse({'error': '只支持POST请求'}, status=405)

# API 接口
@csrf_exempt
def api_coupon_list(request):
    """API: 获取优惠券列表 - 支持分页和搜索"""
    # 获取查询参数
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 10))
    search = request.GET.get('search', '')
    status = request.GET.get('status', '')

    # 构建查询集
    queryset = Coupon.objects.all().order_by('-create_at')

    # 搜索过滤
    if search:
        queryset = queryset.filter(name__icontains=search)

    # 状态过滤
    if status:
        queryset = queryset.filter(status=status)

    # 分页处理
    paginator = Paginator(queryset, page_size)

    try:
        page_obj = paginator.page(page)
    except:
        page_obj = paginator.page(1)

    # 获取当前页数据
    coupon_list = []
    for coupon in page_obj.object_list:
        coupon_data = {
            'id': coupon.id,
            'name': coupon.name,
            'amount': float(coupon.amount),
            'min_amount': float(coupon.min_amount),
            'coupon_code': coupon.coupon_code,
            'status': coupon.status,
            'is_used': coupon.is_used,
            'start_time': coupon.start_time.isoformat(),
            'end_time': coupon.end_time.isoformat(),
            'create_at': coupon.create_at.isoformat(),
            'order_id': coupon.order_id,
            'user_id': coupon.user.id if coupon.user else None,
            'user_name': coupon.user.username if coupon.user else None,
            'is_valid': coupon.is_valid(),
        }
        coupon_list.append(coupon_data)

    # 返回分页信息
    return JsonResponse({
        'data': coupon_list,
        'pagination': {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_count': paginator.count,
            'page_size': page_size,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
        }
    })

@csrf_exempt
def api_coupon_detail(request, coupon_id):
    """API: 获取单个优惠券详情"""
    try:
        coupon = Coupon.objects.get(id=coupon_id)
        data = {
            'id': coupon.id,
            'name': coupon.name,
            'amount': float(coupon.amount),
            'min_amount': float(coupon.min_amount),
            'coupon_code': coupon.coupon_code,
            'status': coupon.status,
            'is_used': coupon.is_used,
            'start_time': coupon.start_time.isoformat(),
            'end_time': coupon.end_time.isoformat(),
            'create_at': coupon.create_at.isoformat(),
            'update_at': coupon.update_at.isoformat(),
            'used_at': coupon.used_at.isoformat() if coupon.used_at else None,
            'order_id': coupon.order_id,
            'user_id': coupon.user.id if coupon.user else None,
            'user_name': coupon.user.username if coupon.user else None,
            'description': coupon.description,
            'is_valid': coupon.is_valid(),
        }
        return JsonResponse(data)
    except Coupon.DoesNotExist:
        return JsonResponse({'error': '优惠券不存在'}, status=404)
