from django.db import models
from django.contrib.auth.models import User

class Coupon(models.Model):
    """优惠券模型"""

    # 优惠券状态选择
    STATUS_CHOICES = [
        ('active', '有效'),
        ('used', '已使用'),
        ('expired', '已过期'),
        ('disabled', '已禁用'),
    ]

    id = models.AutoField(primary_key=True, verbose_name="优惠券ID")
    name = models.CharField(max_length=100, verbose_name="优惠名称")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="优惠金额")
    min_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="最少使用额度")

    # 关联字段
    order_id = models.IntegerField(blank=True, null=True, verbose_name="使用的订单ID")
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, verbose_name="使用的用户")

    # 时间字段
    start_time = models.DateTimeField(verbose_name="有效期开始时间")
    end_time = models.DateTimeField(verbose_name="有效期结束时间")
    create_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    used_at = models.DateTimeField(blank=True, null=True, verbose_name="使用时间")

    # 状态字段
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="优惠券状态")
    is_used = models.BooleanField(default=False, verbose_name="是否已使用")

    # 其他字段
    description = models.TextField(blank=True, verbose_name="优惠券描述")
    coupon_code = models.CharField(max_length=50, unique=True, verbose_name="优惠券代码")

    class Meta:
        app_label = 'coupons'
        verbose_name = "优惠券"
        verbose_name_plural = "优惠券"
        db_table = "coupons"
        ordering = ['-create_at']

    def __str__(self):
        return f"{self.name} - {self.amount}元"

    def is_valid(self):
        """检查优惠券是否有效"""
        from django.utils import timezone
        now = timezone.now()
        return (
            self.status == 'active' and
            not self.is_used and
            self.start_time <= now <= self.end_time
        )

    def can_use_for_amount(self, order_amount):
        """检查是否可以用于指定金额的订单"""
        return self.is_valid() and order_amount >= self.min_amount
