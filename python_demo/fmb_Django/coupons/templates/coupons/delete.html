<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除优惠券 - {{ coupon.name }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .nav-links { margin-bottom: 20px; }
        .nav-links a { margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .nav-links a:hover { background: #0056b3; }
        .warning-card { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 20px; margin-bottom: 20px; }
        .warning-icon { color: #856404; font-size: 24px; margin-bottom: 10px; }
        .coupon-info { background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px; margin-bottom: 20px; }
        .info-row { display: flex; margin-bottom: 10px; }
        .info-label { font-weight: bold; width: 150px; color: #333; }
        .info-value { flex: 1; color: #666; }
        .status-badge { padding: 4px 12px; border-radius: 3px; font-size: 14px; }
        .status-active { background: #d4edda; color: #155724; }
        .status-used { background: #f8d7da; color: #721c24; }
        .status-expired { background: #d1ecf1; color: #0c5460; }
        .status-disabled { background: #e2e3e5; color: #383d41; }
        .actions { margin-top: 30px; }
        .actions button { padding: 12px 24px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 10px; }
        .actions button:hover { background: #c82333; }
        .actions a { padding: 12px 24px; background: #6c757d; color: white; text-decoration: none; border-radius: 3px; }
        .actions a:hover { background: #5a6268; }
        .amount-display { font-size: 18px; color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>删除优惠券</h1>
        <p>确认删除以下优惠券</p>
    </div>

    <div class="nav-links">
        <a href="{% url 'coupons:home' %}">返回首页</a>
        <a href="{% url 'coupons:list' %}">优惠券列表</a>
        <a href="{% url 'coupons:detail' coupon.id %}">查看详情</a>
        <a href="{% url 'coupons:edit' coupon.id %}">编辑优惠券</a>
    </div>

    <div class="warning-card">
        <div class="warning-icon">⚠️</div>
        <h3 style="margin: 0 0 10px 0; color: #856404;">警告</h3>
        <p style="margin: 0; color: #856404;">
            您即将删除此优惠券，此操作不可撤销。请确认您真的要删除这个优惠券。
        </p>
        {% if coupon.is_used %}
        <p style="margin: 10px 0 0 0; color: #721c24; font-weight: bold;">
            ⚠️ 注意：此优惠券已被使用，删除可能会影响相关订单记录。
        </p>
        {% endif %}
    </div>

    <div class="coupon-info">
        <h3>优惠券信息</h3>
        
        <div class="info-row">
            <div class="info-label">优惠券ID:</div>
            <div class="info-value">{{ coupon.id }}</div>
        </div>

        <div class="info-row">
            <div class="info-label">优惠券名称:</div>
            <div class="info-value">{{ coupon.name }}</div>
        </div>

        <div class="info-row">
            <div class="info-label">优惠金额:</div>
            <div class="info-value">
                <span class="amount-display">¥{{ coupon.amount }}</span>
            </div>
        </div>

        <div class="info-row">
            <div class="info-label">最少使用额度:</div>
            <div class="info-value">¥{{ coupon.min_amount }}</div>
        </div>

        <div class="info-row">
            <div class="info-label">优惠券代码:</div>
            <div class="info-value"><code>{{ coupon.coupon_code }}</code></div>
        </div>

        <div class="info-row">
            <div class="info-label">状态:</div>
            <div class="info-value">
                <span class="status-badge status-{{ coupon.status }}">{{ coupon.get_status_display }}</span>
                {% if coupon.is_used %}
                    <span style="margin-left: 10px; color: #721c24;">已使用</span>
                {% endif %}
            </div>
        </div>

        <div class="info-row">
            <div class="info-label">有效期:</div>
            <div class="info-value">
                {{ coupon.start_time|date:"Y-m-d H:i" }} 至 {{ coupon.end_time|date:"Y-m-d H:i" }}
            </div>
        </div>

        <div class="info-row">
            <div class="info-label">创建时间:</div>
            <div class="info-value">{{ coupon.create_at|date:"Y-m-d H:i:s" }}</div>
        </div>

        {% if coupon.user %}
        <div class="info-row">
            <div class="info-label">使用用户:</div>
            <div class="info-value">{{ coupon.user.username }} (ID: {{ coupon.user.id }})</div>
        </div>
        {% endif %}

        {% if coupon.order_id %}
        <div class="info-row">
            <div class="info-label">使用订单:</div>
            <div class="info-value">订单 #{{ coupon.order_id }}</div>
        </div>
        {% endif %}

        {% if coupon.description %}
        <div class="info-row">
            <div class="info-label">描述:</div>
            <div class="info-value">{{ coupon.description }}</div>
        </div>
        {% endif %}
    </div>

    <div class="actions">
        <form method="post" style="display: inline;">
            {% csrf_token %}
            <button type="submit" onclick="return confirm('确定要删除这个优惠券吗？此操作不可撤销！')">
                确认删除
            </button>
        </form>
        <a href="{% url 'coupons:detail' coupon.id %}">取消删除</a>
    </div>
</body>
</html>
