<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券 API 演示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .nav-links { margin-bottom: 20px; }
        .nav-links a { margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .api-section { background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px; margin-bottom: 20px; }
        .api-section h3 { margin-top: 0; color: #333; }
        .api-url { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; border: 1px solid #ddd; margin: 10px 0; }
        .test-button { padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 10px; }
        .test-button:hover { background: #218838; }
        .response-area { background: #f8f9fa; border: 1px solid #ddd; border-radius: 3px; padding: 15px; margin-top: 10px; min-height: 100px; font-family: monospace; white-space: pre-wrap; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { padding: 8px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
        .loading { color: #007bff; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <h1>优惠券 API 演示</h1>
        <p>测试优惠券相关的 API 接口</p>
    </div>

    <div class="nav-links">
        <a href="{% url 'coupons:home' %}">返回首页</a>
        <a href="{% url 'coupons:list' %}">优惠券列表</a>
        <a href="{% url 'coupons:create' %}">创建优惠券</a>
    </div>

    <!-- 获取优惠券列表 -->
    <div class="api-section">
        <h3>1. 获取优惠券列表</h3>
        <div class="api-url">GET /coupons/api/list/</div>
        
        <div class="form-group">
            <label>页码:</label>
            <input type="number" id="list-page" value="1" min="1">
        </div>
        <div class="form-group">
            <label>每页数量:</label>
            <input type="number" id="list-page-size" value="10" min="1" max="100">
        </div>
        <div class="form-group">
            <label>搜索关键词:</label>
            <input type="text" id="list-search" placeholder="搜索优惠券名称">
        </div>
        <div class="form-group">
            <label>状态过滤:</label>
            <select id="list-status">
                <option value="">所有状态</option>
                <option value="active">有效</option>
                <option value="used">已使用</option>
                <option value="expired">已过期</option>
                <option value="disabled">已禁用</option>
            </select>
        </div>
        
        <button class="test-button" onclick="testCouponList()">测试接口</button>
        <div id="list-response" class="response-area">点击"测试接口"查看响应结果...</div>
    </div>

    <!-- 获取优惠券详情 -->
    <div class="api-section">
        <h3>2. 获取优惠券详情</h3>
        <div class="api-url">GET /coupons/api/detail/{id}/</div>
        
        <div class="form-group">
            <label>优惠券ID:</label>
            <input type="number" id="detail-id" value="1" min="1">
        </div>
        
        <button class="test-button" onclick="testCouponDetail()">测试接口</button>
        <div id="detail-response" class="response-area">点击"测试接口"查看响应结果...</div>
    </div>

    <!-- 创建优惠券 -->
    <div class="api-section">
        <h3>3. 创建优惠券</h3>
        <div class="api-url">POST /coupons/api/create/</div>
        
        <div class="form-group">
            <label>优惠券名称:</label>
            <input type="text" id="create-name" value="API测试优惠券">
        </div>
        <div class="form-group">
            <label>优惠金额:</label>
            <input type="number" id="create-amount" value="10" step="0.01" min="0">
        </div>
        <div class="form-group">
            <label>最少使用额度:</label>
            <input type="number" id="create-min-amount" value="50" step="0.01" min="0">
        </div>
        <div class="form-group">
            <label>开始时间:</label>
            <input type="datetime-local" id="create-start-time">
        </div>
        <div class="form-group">
            <label>结束时间:</label>
            <input type="datetime-local" id="create-end-time">
        </div>
        <div class="form-group">
            <label>描述:</label>
            <input type="text" id="create-description" value="通过API创建的测试优惠券">
        </div>
        
        <button class="test-button" onclick="testCouponCreate()">测试接口</button>
        <div id="create-response" class="response-area">点击"测试接口"查看响应结果...</div>
    </div>

    <!-- 使用优惠券 -->
    <div class="api-section">
        <h3>4. 使用优惠券</h3>
        <div class="api-url">POST /coupons/api/use/{id}/</div>
        
        <div class="form-group">
            <label>优惠券ID:</label>
            <input type="number" id="use-id" value="1" min="1">
        </div>
        <div class="form-group">
            <label>订单金额:</label>
            <input type="number" id="use-order-amount" value="100" step="0.01" min="0">
        </div>
        <div class="form-group">
            <label>订单ID:</label>
            <input type="number" id="use-order-id" value="12345" min="1">
        </div>
        <div class="form-group">
            <label>用户ID:</label>
            <input type="number" id="use-user-id" value="1" min="1">
        </div>
        
        <button class="test-button" onclick="testCouponUse()">测试接口</button>
        <div id="use-response" class="response-area">点击"测试接口"查看响应结果...</div>
    </div>

    <script>
        // 设置默认时间
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const startTime = document.getElementById('create-start-time');
            const endTime = document.getElementById('create-end-time');
            
            startTime.value = now.toISOString().slice(0, 16);
            
            const futureDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            endTime.value = futureDate.toISOString().slice(0, 16);
        });

        // 测试获取优惠券列表
        function testCouponList() {
            const page = document.getElementById('list-page').value;
            const pageSize = document.getElementById('list-page-size').value;
            const search = document.getElementById('list-search').value;
            const status = document.getElementById('list-status').value;
            
            let url = `/coupons/api/list/?page=${page}&page_size=${pageSize}`;
            if (search) url += `&search=${encodeURIComponent(search)}`;
            if (status) url += `&status=${status}`;
            
            const responseDiv = document.getElementById('list-response');
            responseDiv.textContent = '正在请求...';
            responseDiv.className = 'response-area loading';
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                    responseDiv.className = 'response-area success';
                })
                .catch(error => {
                    responseDiv.textContent = '请求失败: ' + error.message;
                    responseDiv.className = 'response-area error';
                });
        }

        // 测试获取优惠券详情
        function testCouponDetail() {
            const id = document.getElementById('detail-id').value;
            const url = `/coupons/api/detail/${id}/`;
            
            const responseDiv = document.getElementById('detail-response');
            responseDiv.textContent = '正在请求...';
            responseDiv.className = 'response-area loading';
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                    responseDiv.className = 'response-area success';
                })
                .catch(error => {
                    responseDiv.textContent = '请求失败: ' + error.message;
                    responseDiv.className = 'response-area error';
                });
        }

        // 测试创建优惠券
        function testCouponCreate() {
            const data = {
                name: document.getElementById('create-name').value,
                amount: parseFloat(document.getElementById('create-amount').value),
                min_amount: parseFloat(document.getElementById('create-min-amount').value),
                start_time: document.getElementById('create-start-time').value,
                end_time: document.getElementById('create-end-time').value,
                description: document.getElementById('create-description').value
            };
            
            const responseDiv = document.getElementById('create-response');
            responseDiv.textContent = '正在请求...';
            responseDiv.className = 'response-area loading';
            
            fetch('/coupons/api/create/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                    responseDiv.className = 'response-area success';
                })
                .catch(error => {
                    responseDiv.textContent = '请求失败: ' + error.message;
                    responseDiv.className = 'response-area error';
                });
        }

        // 测试使用优惠券
        function testCouponUse() {
            const id = document.getElementById('use-id').value;
            const data = {
                order_amount: parseFloat(document.getElementById('use-order-amount').value),
                order_id: parseInt(document.getElementById('use-order-id').value),
                user_id: parseInt(document.getElementById('use-user-id').value)
            };
            
            const responseDiv = document.getElementById('use-response');
            responseDiv.textContent = '正在请求...';
            responseDiv.className = 'response-area loading';
            
            fetch(`/coupons/api/use/${id}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                    responseDiv.className = 'response-area success';
                })
                .catch(error => {
                    responseDiv.textContent = '请求失败: ' + error.message;
                    responseDiv.className = 'response-area error';
                });
        }
    </script>
</body>
</html>
