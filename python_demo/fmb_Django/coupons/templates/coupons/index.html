<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-card { background: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }
        .nav-links { margin-bottom: 20px; }
        .nav-links a { margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .nav-links a:hover { background: #0056b3; }
        .coupon-list { background: white; border: 1px solid #ddd; border-radius: 5px; }
        .coupon-item { padding: 15px; border-bottom: 1px solid #eee; }
        .coupon-item:last-child { border-bottom: none; }
        .coupon-name { font-weight: bold; color: #333; }
        .coupon-amount { color: #dc3545; font-size: 18px; font-weight: bold; }
        .coupon-status { padding: 2px 8px; border-radius: 3px; font-size: 12px; }
        .status-active { background: #d4edda; color: #155724; }
        .status-used { background: #f8d7da; color: #721c24; }
        .status-expired { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="header">
        <h1>优惠券管理系统</h1>
        <p>管理和查看所有优惠券信息</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <h3>{{ total_count }}</h3>
            <p>总优惠券数</p>
        </div>
        <div class="stat-card">
            <h3>{{ active_count }}</h3>
            <p>可用优惠券</p>
        </div>
        <div class="stat-card">
            <h3>{{ total_count|add:"-"|add:active_count }}</h3>
            <p>已使用/过期</p>
        </div>
    </div>

    <div class="nav-links">
        <a href="{% url 'coupons:list' %}">查看所有优惠券</a>
        <a href="{% url 'coupons:create' %}">创建新优惠券</a>
        <a href="{% url 'coupons:api_demo' %}">API 演示</a>
        <a href="/admin/">管理后台</a>
    </div>

    <div class="coupon-list">
        <h3 style="padding: 15px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #ddd;">最新优惠券</h3>
        {% for coupon in recent_coupons %}
        <div class="coupon-item">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <div class="coupon-name">{{ coupon.name }}</div>
                    <div style="color: #666; font-size: 14px;">代码: {{ coupon.coupon_code }}</div>
                    <div style="color: #666; font-size: 12px;">创建时间: {{ coupon.create_at|date:"Y-m-d H:i" }}</div>
                </div>
                <div style="text-align: right;">
                    <div class="coupon-amount">¥{{ coupon.amount }}</div>
                    <div style="font-size: 12px; color: #666;">满{{ coupon.min_amount }}可用</div>
                    <span class="coupon-status status-{{ coupon.status }}">{{ coupon.get_status_display }}</span>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="coupon-item">
            <p style="text-align: center; color: #666; margin: 0;">暂无优惠券</p>
        </div>
        {% endfor %}
    </div>

    <div style="margin-top: 20px; text-align: center;">
        <a href="{% url 'coupons:list' %}" style="color: #007bff;">查看更多优惠券 →</a>
    </div>
</body>
</html>
