<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券详情 - {{ coupon.name }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .nav-links { margin-bottom: 20px; }
        .nav-links a { margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .nav-links a:hover { background: #0056b3; }
        .detail-card { background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px; margin-bottom: 20px; }
        .detail-row { display: flex; margin-bottom: 15px; }
        .detail-label { font-weight: bold; width: 150px; color: #333; }
        .detail-value { flex: 1; color: #666; }
        .status-badge { padding: 4px 12px; border-radius: 3px; font-size: 14px; }
        .status-active { background: #d4edda; color: #155724; }
        .status-used { background: #f8d7da; color: #721c24; }
        .status-expired { background: #d1ecf1; color: #0c5460; }
        .status-disabled { background: #e2e3e5; color: #383d41; }
        .amount-display { font-size: 24px; color: #dc3545; font-weight: bold; }
        .code-display { background: #f8f9fa; padding: 8px; border-radius: 3px; font-family: monospace; border: 1px solid #ddd; }
        .actions { margin-top: 20px; }
        .actions a { margin-right: 10px; padding: 10px 20px; text-decoration: none; border-radius: 3px; }
        .btn-edit { background: #ffc107; color: #212529; }
        .btn-delete { background: #dc3545; color: white; }
        .btn-back { background: #6c757d; color: white; }
        .validity-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 15px; }
        .valid { color: #155724; }
        .invalid { color: #721c24; }
    </style>
</head>
<body>
    <div class="header">
        <h1>优惠券详情</h1>
        <p>查看优惠券的详细信息</p>
    </div>

    <div class="nav-links">
        <a href="{% url 'coupons:home' %}">返回首页</a>
        <a href="{% url 'coupons:list' %}">优惠券列表</a>
        <a href="{% url 'coupons:edit' coupon.id %}">编辑优惠券</a>
    </div>

    <div class="detail-card">
        <h3>基本信息</h3>
        
        <div class="detail-row">
            <div class="detail-label">优惠券ID:</div>
            <div class="detail-value">{{ coupon.id }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">优惠券名称:</div>
            <div class="detail-value">{{ coupon.name }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">优惠金额:</div>
            <div class="detail-value">
                <span class="amount-display">¥{{ coupon.amount }}</span>
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">最少使用额度:</div>
            <div class="detail-value">¥{{ coupon.min_amount }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">优惠券代码:</div>
            <div class="detail-value">
                <div class="code-display">{{ coupon.coupon_code }}</div>
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">状态:</div>
            <div class="detail-value">
                <span class="status-badge status-{{ coupon.status }}">{{ coupon.get_status_display }}</span>
                {% if coupon.is_used %}
                    <span style="margin-left: 10px; color: #721c24;">已使用</span>
                {% endif %}
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">描述:</div>
            <div class="detail-value">{{ coupon.description|default:"无描述" }}</div>
        </div>
    </div>

    <div class="detail-card">
        <h3>时间信息</h3>
        
        <div class="detail-row">
            <div class="detail-label">有效期开始:</div>
            <div class="detail-value">{{ coupon.start_time|date:"Y年m月d日 H:i:s" }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">有效期结束:</div>
            <div class="detail-value">{{ coupon.end_time|date:"Y年m月d日 H:i:s" }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">创建时间:</div>
            <div class="detail-value">{{ coupon.create_at|date:"Y年m月d日 H:i:s" }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">更新时间:</div>
            <div class="detail-value">{{ coupon.update_at|date:"Y年m月d日 H:i:s" }}</div>
        </div>

        {% if coupon.used_at %}
        <div class="detail-row">
            <div class="detail-label">使用时间:</div>
            <div class="detail-value">{{ coupon.used_at|date:"Y年m月d日 H:i:s" }}</div>
        </div>
        {% endif %}

        <div class="validity-info">
            {% if coupon.is_valid %}
                <div class="valid">✓ 此优惠券当前有效，可以使用</div>
            {% else %}
                <div class="invalid">✗ 此优惠券当前无效</div>
            {% endif %}
        </div>
    </div>

    {% if coupon.user or coupon.order_id %}
    <div class="detail-card">
        <h3>使用信息</h3>
        
        {% if coupon.user %}
        <div class="detail-row">
            <div class="detail-label">使用用户:</div>
            <div class="detail-value">{{ coupon.user.username }} (ID: {{ coupon.user.id }})</div>
        </div>
        {% endif %}

        {% if coupon.order_id %}
        <div class="detail-row">
            <div class="detail-label">使用订单:</div>
            <div class="detail-value">订单 #{{ coupon.order_id }}</div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <div class="actions">
        <a href="{% url 'coupons:list' %}" class="btn-back">返回列表</a>
        <a href="{% url 'coupons:edit' coupon.id %}" class="btn-edit">编辑优惠券</a>
        <a href="{% url 'coupons:delete' coupon.id %}" class="btn-delete">删除优惠券</a>
    </div>
</body>
</html>
