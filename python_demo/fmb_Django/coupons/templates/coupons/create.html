<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建优惠券</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .nav-links { margin-bottom: 20px; }
        .nav-links a { margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .nav-links a:hover { background: #0056b3; }
        .form-card { background: white; border: 1px solid #ddd; border-radius: 5px; padding: 20px; max-width: 600px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px; box-sizing: border-box; }
        .form-group textarea { height: 80px; resize: vertical; }
        .form-group input[type="datetime-local"] { width: 100%; }
        .form-actions { margin-top: 30px; }
        .form-actions button { padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 10px; }
        .form-actions button:hover { background: #218838; }
        .form-actions a { padding: 12px 24px; background: #6c757d; color: white; text-decoration: none; border-radius: 3px; }
        .form-actions a:hover { background: #5a6268; }
        .help-text { font-size: 12px; color: #666; margin-top: 5px; }
        .required { color: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>创建优惠券</h1>
        <p>填写以下信息创建新的优惠券</p>
    </div>

    <div class="nav-links">
        <a href="{% url 'coupons:home' %}">返回首页</a>
        <a href="{% url 'coupons:list' %}">优惠券列表</a>
    </div>

    <div class="form-card">
        <form method="post">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="name">优惠券名称 <span class="required">*</span></label>
                <input type="text" id="name" name="name" required>
                <div class="help-text">请输入优惠券的名称，例如：新用户专享优惠券</div>
            </div>

            <div class="form-group">
                <label for="amount">优惠金额 <span class="required">*</span></label>
                <input type="number" id="amount" name="amount" step="0.01" min="0" required>
                <div class="help-text">优惠券的面值金额，单位：元</div>
            </div>

            <div class="form-group">
                <label for="min_amount">最少使用额度</label>
                <input type="number" id="min_amount" name="min_amount" step="0.01" min="0" value="0">
                <div class="help-text">使用此优惠券的最低订单金额要求，0表示无限制</div>
            </div>

            <div class="form-group">
                <label for="start_time">有效期开始时间 <span class="required">*</span></label>
                <input type="datetime-local" id="start_time" name="start_time" required>
                <div class="help-text">优惠券开始生效的时间</div>
            </div>

            <div class="form-group">
                <label for="end_time">有效期结束时间 <span class="required">*</span></label>
                <input type="datetime-local" id="end_time" name="end_time" required>
                <div class="help-text">优惠券失效的时间</div>
            </div>

            <div class="form-group">
                <label for="description">优惠券描述</label>
                <textarea id="description" name="description" placeholder="请输入优惠券的详细描述..."></textarea>
                <div class="help-text">可选，描述优惠券的使用条件和说明</div>
            </div>

            <div class="form-actions">
                <button type="submit">创建优惠券</button>
                <a href="{% url 'coupons:list' %}">取消</a>
            </div>
        </form>
    </div>

    <script>
        // 设置默认时间
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const startTime = document.getElementById('start_time');
            const endTime = document.getElementById('end_time');
            
            // 设置开始时间为当前时间
            startTime.value = now.toISOString().slice(0, 16);
            
            // 设置结束时间为30天后
            const futureDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            endTime.value = futureDate.toISOString().slice(0, 16);
            
            // 验证结束时间必须大于开始时间
            function validateDates() {
                if (startTime.value && endTime.value) {
                    if (new Date(endTime.value) <= new Date(startTime.value)) {
                        endTime.setCustomValidity('结束时间必须大于开始时间');
                    } else {
                        endTime.setCustomValidity('');
                    }
                }
            }
            
            startTime.addEventListener('change', validateDates);
            endTime.addEventListener('change', validateDates);
        });
    </script>
</body>
</html>
