<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券列表</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .search-form { background: white; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 20px; }
        .search-form input, .search-form select { padding: 8px; margin-right: 10px; border: 1px solid #ddd; border-radius: 3px; }
        .search-form button { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .nav-links { margin-bottom: 20px; }
        .nav-links a { margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .nav-links a:hover { background: #0056b3; }
        .coupon-table { width: 100%; border-collapse: collapse; background: white; border: 1px solid #ddd; border-radius: 5px; }
        .coupon-table th, .coupon-table td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        .coupon-table th { background: #f8f9fa; font-weight: bold; }
        .coupon-table tr:hover { background: #f8f9fa; }
        .status-badge { padding: 2px 8px; border-radius: 3px; font-size: 12px; }
        .status-active { background: #d4edda; color: #155724; }
        .status-used { background: #f8d7da; color: #721c24; }
        .status-expired { background: #d1ecf1; color: #0c5460; }
        .status-disabled { background: #e2e3e5; color: #383d41; }
        .pagination { text-align: center; margin-top: 20px; }
        .pagination a { padding: 8px 12px; margin: 0 2px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .pagination .current { padding: 8px 12px; margin: 0 2px; background: #6c757d; color: white; border-radius: 3px; }
        .actions a { margin-right: 5px; padding: 4px 8px; text-decoration: none; border-radius: 3px; font-size: 12px; }
        .btn-view { background: #17a2b8; color: white; }
        .btn-edit { background: #ffc107; color: #212529; }
        .btn-delete { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>优惠券列表</h1>
        <p>查看和管理所有优惠券</p>
    </div>

    <div class="nav-links">
        <a href="{% url 'coupons:home' %}">返回首页</a>
        <a href="{% url 'coupons:create' %}">创建新优惠券</a>
        <a href="{% url 'coupons:api_demo' %}">API 演示</a>
    </div>

    <div class="search-form">
        <form method="get">
            <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: end;">
                <div>
                    <label>搜索关键词:</label>
                    <input type="text" name="search" placeholder="名称或代码..." value="{{ search }}" style="width: 150px;">
                </div>

                <div>
                    <label>状态:</label>
                    <select name="status">
                        <option value="">所有状态</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label>使用状态:</label>
                    <select name="is_used">
                        <option value="">全部</option>
                        <option value="0" {% if is_used == '0' %}selected{% endif %}>未使用</option>
                        <option value="1" {% if is_used == '1' %}selected{% endif %}>已使用</option>
                    </select>
                </div>

                <div>
                    <label>金额范围:</label>
                    <input type="number" name="min_amount" placeholder="最小金额" value="{{ min_amount }}" style="width: 80px;" step="0.01">
                    <span style="margin: 0 5px;">-</span>
                    <input type="number" name="max_amount" placeholder="最大金额" value="{{ max_amount }}" style="width: 80px;" step="0.01">
                </div>

                <div>
                    <label>有效性:</label>
                    <select name="validity">
                        <option value="">全部</option>
                        <option value="valid" {% if validity == 'valid' %}selected{% endif %}>当前有效</option>
                        <option value="expired" {% if validity == 'expired' %}selected{% endif %}>已过期</option>
                        <option value="future" {% if validity == 'future' %}selected{% endif %}>未来生效</option>
                    </select>
                </div>

                <div>
                    <label>排序:</label>
                    <select name="order_by">
                        {% for value, label in order_choices %}
                        <option value="{{ value }}" {% if order_by == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label>每页显示:</label>
                    <select name="page_size">
                        {% for size in page_size_choices %}
                        <option value="{{ size }}" {% if page_size == size %}selected{% endif %}>{{ size }}条</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <button type="submit">搜索</button>
                    <a href="{% url 'coupons:list' %}" style="margin-left: 5px; padding: 8px 16px; background: #6c757d; color: white; text-decoration: none; border-radius: 3px;">清除</a>
                </div>
            </div>
        </form>
    </div>

    <table class="coupon-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>优惠券名称</th>
                <th>优惠金额</th>
                <th>最少使用额度</th>
                <th>优惠券代码</th>
                <th>状态</th>
                <th>有效期</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for coupon in page_obj %}
            <tr>
                <td>{{ coupon.id }}</td>
                <td>{{ coupon.name }}</td>
                <td style="color: #dc3545; font-weight: bold;">¥{{ coupon.amount }}</td>
                <td>¥{{ coupon.min_amount }}</td>
                <td><code>{{ coupon.coupon_code }}</code></td>
                <td><span class="status-badge status-{{ coupon.status }}">{{ coupon.get_status_display }}</span></td>
                <td>
                    <div style="font-size: 12px;">
                        <div>{{ coupon.start_time|date:"Y-m-d H:i" }}</div>
                        <div>{{ coupon.end_time|date:"Y-m-d H:i" }}</div>
                    </div>
                </td>
                <td>{{ coupon.create_at|date:"Y-m-d H:i" }}</td>
                <td class="actions">
                    <a href="{% url 'coupons:detail' coupon.id %}" class="btn-view">查看</a>
                    <a href="{% url 'coupons:edit' coupon.id %}" class="btn-edit">编辑</a>
                    <a href="{% url 'coupons:delete' coupon.id %}" class="btn-delete">删除</a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" style="text-align: center; color: #666;">暂无优惠券数据</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- 分页 -->
    {% if page_obj.has_other_pages %}
    <div class="pagination">
        {% if page_obj.has_previous %}
            <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if is_used %}&is_used={{ is_used }}{% endif %}{% if min_amount %}&min_amount={{ min_amount }}{% endif %}{% if max_amount %}&max_amount={{ max_amount }}{% endif %}{% if validity %}&validity={{ validity }}{% endif %}{% if order_by %}&order_by={{ order_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}">&laquo; 首页</a>
            <a href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if is_used %}&is_used={{ is_used }}{% endif %}{% if min_amount %}&min_amount={{ min_amount }}{% endif %}{% if max_amount %}&max_amount={{ max_amount }}{% endif %}{% if validity %}&validity={{ validity }}{% endif %}{% if order_by %}&order_by={{ order_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}">上一页</a>
        {% endif %}

        <span class="current">
            第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页 (共{{ page_obj.paginator.count }}条记录)
        </span>

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if is_used %}&is_used={{ is_used }}{% endif %}{% if min_amount %}&min_amount={{ min_amount }}{% endif %}{% if max_amount %}&max_amount={{ max_amount }}{% endif %}{% if validity %}&validity={{ validity }}{% endif %}{% if order_by %}&order_by={{ order_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}">下一页</a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if is_used %}&is_used={{ is_used }}{% endif %}{% if min_amount %}&min_amount={{ min_amount }}{% endif %}{% if max_amount %}&max_amount={{ max_amount }}{% endif %}{% if validity %}&validity={{ validity }}{% endif %}{% if order_by %}&order_by={{ order_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}">末页 &raquo;</a>
        {% endif %}
    </div>
    {% endif %}
</body>
</html>
