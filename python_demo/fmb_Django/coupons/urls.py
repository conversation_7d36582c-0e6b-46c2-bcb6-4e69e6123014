from django.urls import path
from . import views

app_name = 'coupons'

urlpatterns = [
    # 页面路由
    path('', views.home, name='home'),
    path('list/', views.coupon_list, name='list'),
    path('detail/<int:coupon_id>/', views.coupon_detail, name='detail'),
    path('create/', views.coupon_create, name='create'),
    path('edit/<int:coupon_id>/', views.coupon_edit, name='edit'),
    path('delete/<int:coupon_id>/', views.coupon_delete, name='delete'),
    path('api-demo/', views.api_demo, name='api_demo'),
    
    # API 路由
    path('api/list/', views.api_coupon_list, name='api_list'),
    path('api/detail/<int:coupon_id>/', views.api_coupon_detail, name='api_detail'),
    path('api/create/', views.api_coupon_create, name='api_create'),
    path('api/use/<int:coupon_id>/', views.api_coupon_use, name='api_use'),
]
