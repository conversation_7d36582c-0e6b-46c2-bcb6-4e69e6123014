"""
URL configuration for fmb_Django project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse

def home(request):
    return HttpResponse("""
    <h1>Django 项目首页</h1>
    <ul>
        <li><a href="/admin/">管理后台</a></li>
        <li><a href="/blog/hello/">博客</a></li>
        <li><a href="/dianping/index/">点评</a></li>
        <li><a href="/custom_admin/index/">自定义管理</a></li>
        <li><a href="/goods/">商品管理</a></li>
        <li><a href="/coupons/">优惠券管理</a></li>
    </ul>
    """)

urlpatterns = [
    path("", home, name="home"),
    path("admin/", admin.site.urls),
    path("blog/", include("blog.urls")),
    path("dianping/", include("dianping.urls")),
    path("custom_admin/", include("custom_admin.urls")),
    path("goods/", include("goods.urls")),  # 添加 goods 路由
    path("coupons/", include("coupons.urls")),  # 添加 coupons 路由
]
