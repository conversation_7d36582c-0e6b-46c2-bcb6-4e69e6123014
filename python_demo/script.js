// 游戏常量
const GRID_SIZE = 20;
const CELL_SIZE = 20;
const GAME_WIDTH = 400;
const GAME_HEIGHT = 400;

// 游戏状态
let snake = [{x: 10, y: 10}];
let food = generateFood();
let direction = 'RIGHT';
let gameSpeed = 150;
let gameOver = false;
let gameInterval;

// 初始化游戏
function initGame() {
    const canvas = document.getElementById('gameCanvas');
    const ctx = canvas.getContext('2d');
    
    // 监听键盘事件
    document.addEventListener('keydown', changeDirection);
    
    // 开始游戏循环
    gameInterval = setInterval(() => {
        if (!gameOver) {
            moveSnake();
            checkCollision();
            drawGame(ctx);
        }
    }, gameSpeed);
}

// 绘制游戏
function drawGame(ctx) {
    // 清空画布
    ctx.clearRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
    
    // 绘制蛇
    ctx.fillStyle = 'green';
    snake.forEach(segment => {
        ctx.fillRect(segment.x * CELL_SIZE, segment.y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
    });
    
    // 绘制食物
    ctx.fillStyle = 'red';
    ctx.fillRect(food.x * CELL_SIZE, food.y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
}

// 移动蛇
function moveSnake() {
    const head = {...snake[0]};
    
    switch(direction) {
        case 'UP':
            head.y--;
            break;
        case 'DOWN':
            head.y++;
            break;
        case 'LEFT':
            head.x--;
            break;
        case 'RIGHT':
            head.x++;
            break;
    }
    
    // 添加新头部
    snake.unshift(head);
    
    // 检查是否吃到食物
    if (head.x === food.x && head.y === food.y) {
        food = generateFood();
    } else {
        // 移除尾部
        snake.pop();
    }
}

// 改变方向
function changeDirection(e) {
    const key = e.keyCode;
    
    // 防止180度转向
    if (key === 37 && direction !== 'RIGHT') direction = 'LEFT';
    else if (key === 38 && direction !== 'DOWN') direction = 'UP';
    else if (key === 39 && direction !== 'LEFT') direction = 'RIGHT';
    else if (key === 40 && direction !== 'UP') direction = 'DOWN';
}

// 检查碰撞
function checkCollision() {
    const head = snake[0];
    
    // 检查墙壁碰撞
    if (head.x < 0 || head.x >= GRID_SIZE || head.y < 0 || head.y >= GRID_SIZE) {
        gameOver = true;
        clearInterval(gameInterval);
        alert('游戏结束!');
    }
    
    // 检查自身碰撞
    for (let i = 1; i < snake.length; i++) {
        if (head.x === snake[i].x && head.y === snake[i].y) {
            gameOver = true;
            clearInterval(gameInterval);
            alert('游戏结束!');
            break;
        }
    }
}

// 生成食物
function generateFood() {
    const food = {
        x: Math.floor(Math.random() * GRID_SIZE),
        y: Math.floor(Math.random() * GRID_SIZE)
    };
    
    // 确保食物不会出现在蛇身上
    for (let segment of snake) {
        if (food.x === segment.x && food.y === segment.y) {
            return generateFood();
        }
    }
    
    return food;
}

// 启动游戏
window.onload = initGame;