import requests
import json

url = 'https://www.zhipin.com/wapi/zpgeek/search/joblist.json?page=1&pageSize=15&city=*********&expectInfo=&query=python&multiSubway=&multiBusinessDistrict=&position=&jobType=&salary=&experience=&degree=&industry=&scale=&stage=&scene=1&_=*************'


headers = {
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    # 'cookie': 'ab_guid=65ca185c-e267-4cd5-b7ab-9182dcf59e37; lastCity=*********; Hm_lvt_194df3105ad7148dcf2b98a91b5e727a=**********,**********; HMACCOUNT=317680DB6A1F48C3; __g=sem_pz_bdpc_dasou_title; __l=l=%2Fwww.zhipin.com%2Fsem%2F10.html%3Fsid%3Dsem_pz_bdpc_dasou_title&r=https%3A%2F%2Fwww.baidu.com%2Fother.php%3Fsc.060000a7zfl63nxQ1rYS_4-Qh2m3N_vN0LdvnPqqTd6NtoV7Myngck4qkoPF6ssGR5c2TBs6dSyfgup2YsOznDYHOTcewQ2Ngd-3jk4dABvP3-9zfKcrDccYlBRcPNcVXGs2kobmMRkWwuxIwdKjmG7dJ6axvIxeGKhdD-50VqWQzf9BaXV4isrRgJJLL7QsyjlsFg0-jIzi8hpS4JUnPRyxcvWs.7b_NR2Ar5Od663rj6t8AGSPticcYlm2erxX5DkAMHz3SZ-E8kstV1nU_ZQfxfCGRdrYG4TXGmuCy2SMF3tf.TLFWgv-b5HDkrfK1ThPGujYknHb0THdBULP1doZA80KYUHdBULP1doZA80KdTvNzgLw4TARqn0K9u7qYXgK-5Hn0IvqzujdBULP1doZA80KzmLmqn0KdThkxpyfqnHRvrHDznjb1PfKVINqGujYkPWTsnW04PsKVgv-b5HDYnj6kPjnd0AdYTAkxpyfqnHc3nWm0TZuxpyfqn0KGuAnqiDFK0ZKGujYk0APGujY1rjc0mLFW5HRYP16L%26ck%3D1876.6.71.402.150.359.194.1%26dt%3D1753177638%26wd%3Dboss%25E7%259B%25B4%25E8%2581%2598%26tpl%3Dtpl_12826_36977_0%26l%3D1569120935%26ai%3D0_60872259_1_1%26us%3DlinkVersion%253D1%2526compPath%253D10036.0-10032.0%2526label%253D%2525E4%2525B8%2525BB%2525E6%2525A0%252587%2525E9%2525A2%252598%2526linkType%253D%2526linkText%253DBOSS%2525E7%25259B%2525B4%2525E8%252581%252598%2525E2%252580%252594%2525E2%252580%252594%2525E6%252589%2525BE%2525E5%2525B7%2525A5%2525E4%2525BD%25259C%2525EF%2525BC%25258C%2525E4%2525B8%25258ABOSS%2525E7%25259B%2525B4%2525E8%252581%252598%2525EF%2525BC%25258C&g=%2Fwww.zhipin.com%2Fsem%2F10.html%3Fsid%3Dsem_pz_bdpc_dasou_title&s=3&friend_source=0; Hm_lpvt_194df3105ad7148dcf2b98a91b5e727a=1753179244; __zp_stoken__=56b9fw49rwp8GPQIAUH0DwrtMekYHSHvCtF5ufsKydsK2Q8K3a1lPRldLTMKzXMKjQ0vCqsKWwqHCplnCtMK7wpFOwqnCu8KIUMOgwqHCn8Sfw6nCjcOqwpDCtcK7wpw%2FIQ4MDw8JAAIBAQcAAggIDgQGBQUDDgwPDwk8J8O4wrY0Pj0zOCxAQkEKQFFUSFdMC15JSz49XF4DAT0hEjg%2BOcKxwqfCvcOGwrPDu8K9w4fCtMO5wr9bODY5O8K%2Fw5crNUMMwr9aDcK8wr4MZlwiDMOIVsK4X0PCuMKpJD0%2Fwrk1PhkyPzkwPD8wOT4pPwrDjlHCulxDwr7Cmiw4HzE%2BPDY9OT48ND83KjwwdC8%2BOygzCwwDDgIoM8K%2FwpDCuMOTPjw%3D; __c=**********; __a=58004993.**********.**********.**********.**********',
    'user-agent' : 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'referer' : 'https://www.zhipin.com/web/geek/jobs?query=python&city=*********'
}


response = requests.get(url,headers=headers)






response = requests.get(url,headers=headers)
print(response.json())
# with(open('boss.csv','w')) as f:
# with open("boss.csv","w",encoding="utf-8") as f:
#     for w in response.json()['zpData']['jobList']:
#         f.write(f'{w.get("jobName")},{w.get("salaryDesc")},{w.get("jobDegree")}')
        # print(f' {w.get("jobName")}---{w.get("salaryDesc")}---{w.get("jobDegree")}')

# data = response.json()['zpData']['jobList']
# print(data)
