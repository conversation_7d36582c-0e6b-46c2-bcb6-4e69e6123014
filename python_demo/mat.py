import requests
from bs4 import BeautifulSoup
import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # 或 'Heiti SC', 'SimHei'
matplotlib.rcParams['axes.unicode_minus'] = False

# 1. 获取商品列表
url = 'https://search.jd.com/Search?keyword=手机&enc=utf-8&wq=手机&pvid=1234567890'
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
}
response = requests.get(url, headers=headers)
response.encoding = 'utf-8'

# 2. 解析商品标题
soup = BeautifulSoup(response.text, 'html.parser')

print(response.text)
print(soup)


# 抓取百度搜索结果
url = 'https://www.baidu.com/s?wd=手机'
response = requests.get(url, headers=headers)
response.encoding = 'utf-8'
print(response.text)


# titles = [tag.text.strip() for tag in soup.select('.p-name em') if tag.text.strip()]

# # 3. 打印商品标题
# for i, title in enumerate(titles, 1):
#     print(f"{i}: {title}")

# 4. 可视化商品数量
# plt.bar(range(len(titles)), [1]*len(titles))
# plt.xlabel('商品序号')
# plt.ylabel('数量')
# plt.title('京东手机商品数量示意')
# plt.show()