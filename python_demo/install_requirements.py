#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装股票筛选器所需的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("股票筛选器依赖安装程序")
    print("=" * 40)
    
    # 需要安装的包
    packages = [
        "akshare",
        "pandas", 
        "requests",
        "numpy"
    ]
    
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 40)
    print(f"安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("✅ 所有依赖安装成功，可以运行股票筛选器了！")
        print("\n运行命令:")
        print("python stock_screener_akshare.py")
    else:
        print("❌ 部分依赖安装失败，请手动安装")
        print("\n手动安装命令:")
        for package in packages:
            print(f"pip install {package}")

if __name__ == "__main__":
    main()
